module.exports = {

"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "checkShortCodeExists": (()=>checkShortCodeExists),
    "createShortUrl": (()=>createShortUrl),
    "getPool": (()=>getPool),
    "getRecentLinks": (()=>getRecentLinks),
    "getUrlByShortCode": (()=>getUrlByShortCode),
    "incrementClicks": (()=>incrementClicks),
    "initDatabase": (()=>initDatabase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// Database connection pool
let pool = null;
function getPool() {
    if (!pool) {
        pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"]({
            connectionString: process.env.DATABASE_URL,
            ssl: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false
        });
    }
    return pool;
}
async function initDatabase() {
    const client = getPool();
    try {
        // Create shortened_links table
        await client.query(`
      CREATE TABLE IF NOT EXISTS shortened_links (
        id SERIAL PRIMARY KEY,
        short_code VARCHAR(10) UNIQUE NOT NULL,
        original_url TEXT NOT NULL,
        clicks INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // Create index for faster lookups
        await client.query(`
      CREATE INDEX IF NOT EXISTS idx_short_code ON shortened_links(short_code)
    `);
        console.log('Database initialized successfully');
    } catch (error) {
        console.error('Database initialization error:', error);
        throw error;
    }
}
async function createShortUrl(shortCode, originalUrl) {
    const client = getPool();
    const result = await client.query('INSERT INTO shortened_links (short_code, original_url) VALUES ($1, $2) RETURNING *', [
        shortCode,
        originalUrl
    ]);
    return result.rows[0];
}
async function getUrlByShortCode(shortCode) {
    const client = getPool();
    const result = await client.query('SELECT * FROM shortened_links WHERE short_code = $1', [
        shortCode
    ]);
    return result.rows[0] || null;
}
async function incrementClicks(shortCode) {
    const client = getPool();
    const result = await client.query('UPDATE shortened_links SET clicks = clicks + 1, updated_at = CURRENT_TIMESTAMP WHERE short_code = $1 RETURNING clicks', [
        shortCode
    ]);
    return result.rows[0]?.clicks || 0;
}
async function getRecentLinks(limit = 10) {
    const client = getPool();
    const result = await client.query('SELECT * FROM shortened_links ORDER BY created_at DESC LIMIT $1', [
        limit
    ]);
    return result.rows;
}
async function checkShortCodeExists(shortCode) {
    const client = getPool();
    const result = await client.query('SELECT 1 FROM shortened_links WHERE short_code = $1', [
        shortCode
    ]);
    return result.rows.length > 0;
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__50079a87._.js.map