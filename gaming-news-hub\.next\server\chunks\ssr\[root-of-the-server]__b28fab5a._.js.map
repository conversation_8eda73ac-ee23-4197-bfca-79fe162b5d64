{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\n\ninterface LinkData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function RedirectPage() {\n  const params = useParams();\n  const shortCode = params.shortCode as string;\n\n  const [linkData, setLinkData] = useState<LinkData | null>(null);\n  const [countdown, setCountdown] = useState(30); // 30 seconds countdown\n  const [showAd, setShowAd] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [adClicked, setAdClicked] = useState(false);\n  const [countdownStarted, setCountdownStarted] = useState(false);\n  const [fallbackTimer, setFallbackTimer] = useState(50); // 50 second fallback\n  const [bannerAdsLoaded, setBannerAdsLoaded] = useState(false);\n  const [montageAdsLoaded, setMontageAdsLoaded] = useState(false);\n\n  useEffect(() => {\n    // Fetch the original URL\n    const fetchLinkData = async () => {\n      try {\n        const response = await fetch(`/api/redirect/${shortCode}`);\n        if (!response.ok) {\n          throw new Error('Link not found');\n        }\n        const data = await response.json();\n        setLinkData(data);\n        setLoading(false);\n      } catch {\n        setError('Link not found or expired');\n        setLoading(false);\n      }\n    };\n\n    fetchLinkData();\n  }, [shortCode]);\n\n  // Handle countdown only after ad is clicked\n  useEffect(() => {\n    if (!countdownStarted || countdown <= 0) return;\n\n    const timer = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          clearInterval(timer);\n          handleRedirect();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [countdownStarted, countdown]);\n\n  // Handle fallback timer (50 seconds) - allows redirect if no ad interaction\n  useEffect(() => {\n    if (countdownStarted) return; // Don't run fallback if countdown already started\n\n    const fallbackInterval = setInterval(() => {\n      setFallbackTimer((prev) => {\n        if (prev <= 1) {\n          clearInterval(fallbackInterval);\n          // If no ad interaction after 50 seconds, allow redirect\n          if (linkData?.originalUrl) {\n            handleRedirect();\n          }\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(fallbackInterval);\n  }, [countdownStarted, linkData]);\n\n  const handleAdClick = () => {\n    console.log('Ad clicked! Starting 30-second countdown...');\n    setAdClicked(true);\n    setCountdownStarted(true);\n    // Reset countdown to 30 seconds when ad is clicked\n    setCountdown(30);\n  };\n\n  // Load banner ads after component mounts\n  useEffect(() => {\n    if (!loading && !error) {\n      // Set a small delay to ensure the page is fully loaded\n      const timer = setTimeout(() => {\n        setBannerAdsLoaded(true);\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [loading, error]);\n\n  const handleSkipAd = () => {\n    if (countdown <= 0) {\n      setShowAd(false);\n    }\n  };\n\n  const handleRedirect = () => {\n    if (linkData) {\n      // Track the click\n      fetch(`/api/redirect/${shortCode}`, { method: 'POST' });\n      // Redirect to original URL\n      window.location.href = linkData.originalUrl;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-white\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-white mb-4\">404</h1>\n          <p className=\"text-gray-300 mb-8\">{error}</p>\n          <Link href=\"/\" className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all\">\n            Go Home\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (showAd) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n        {/* Header */}\n        <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n          <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n              🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n            </Link>\n            <div className=\"text-purple-400 font-mono\">\n              Redirecting in {countdown}s\n            </div>\n          </div>\n        </nav>\n\n        <div className=\"flex items-center justify-center min-h-[calc(100vh-80px)] p-4\">\n          <div className=\"max-w-2xl w-full\">\n            {/* Ad Section */}\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n              <h2 className=\"text-2xl font-bold text-white mb-4 text-center\">\n                {!countdownStarted ? (\n                  <>🎮 Click the Ad to Continue to Your Link!</>\n                ) : (\n                  <>⏰ Redirecting in {countdown} seconds...</>\n                )}\n              </h2>\n\n              {!countdownStarted && (\n                <div className=\"text-center mb-4\">\n                  <p className=\"text-yellow-300 text-sm\">\n                    ⚠️ You must click the advertisement below to start the countdown\n                  </p>\n                  <p className=\"text-gray-400 text-xs mt-1\">\n                    Or wait {fallbackTimer} seconds for automatic redirect\n                  </p>\n                </div>\n              )}\n\n              {/* Banner Ad Code Container */}\n              <div className=\"bg-white/5 rounded-lg p-6 mb-6 text-center\">\n                <p className=\"text-gray-300 text-sm mb-4\">Advertisement - Click any banner to continue</p>\n                <div\n                  id=\"banner-ad-container\"\n                  className=\"min-h-[200px] flex items-center justify-center cursor-pointer\"\n                  onClick={handleAdClick}\n                >\n                  {bannerAdsLoaded ? (\n                    <div className=\"w-full\">\n                      {/* ========== PASTE YOUR BANNER AD HTML HERE ========== */}\n\n\n\n                      {/* ========== END BANNER AD HTML ========== */}\n\n                      <div className=\"text-gray-400 text-sm\">\n                        {adClicked ? 'Ad clicked! Countdown started' : 'Click the banner ad above to start countdown'}\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"text-gray-400 text-sm\">Loading banner ads...</div>\n                  )}\n                </div>\n              </div>\n\n              {/* Fallback Gaming Ad - Always clickable */}\n              <div\n                onClick={handleAdClick}\n                className={`bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 cursor-pointer hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 mb-6 ${\n                  adClicked ? 'opacity-50 pointer-events-none' : ''\n                }`}\n              >\n                <div className=\"text-center text-white\">\n                  <h3 className=\"text-lg font-bold mb-2\">🚀 Epic Gaming Deals!</h3>\n                  <p className=\"mb-3\">Get up to 70% off on the latest games and gaming gear!</p>\n                  <div className=\"bg-white/20 rounded-lg p-3\">\n                    <p className=\"text-sm font-semibold\">\n                      {adClicked ? 'Ad Clicked! Countdown Started' : 'Click Here to Continue'}\n                    </p>\n                    <p className=\"text-xs opacity-90\">\n                      {adClicked ? 'Redirecting soon...' : 'Click to start 30-second countdown'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Countdown Display */}\n              <div className=\"text-center\">\n                {countdownStarted ? (\n                  <>\n                    <div className=\"bg-green-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold animate-pulse\">\n                      {countdown}\n                    </div>\n                    <p className=\"text-green-300 mb-4\">\n                      ✅ Ad clicked! Redirecting in {countdown} seconds...\n                    </p>\n                  </>\n                ) : (\n                  <>\n                    <div className=\"bg-yellow-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold\">\n                      {fallbackTimer}\n                    </div>\n                    <p className=\"text-yellow-300 mb-4\">\n                      ⏳ Click an ad above or wait {fallbackTimer} seconds for automatic redirect\n                    </p>\n                  </>\n                )}\n\n                <div className=\"bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold\">\n                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}\n                </div>\n              </div>\n            </div>\n\n            {/* Link Preview */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-4\">\n              <p className=\"text-gray-400 text-sm mb-2\">You will be redirected to:</p>\n              <p className=\"text-purple-400 font-mono break-all\">{linkData?.originalUrl}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* ========== PASTE YOUR BANNER AD SCRIPT HERE ========== */}\n\n\n\n        {/* ========== END BANNER AD SCRIPT ========== */}\n\n        {/* Montag Ad Network Script */}\n        <Script\n          src=\"https://fpyf8.com/88/tag.min.js\"\n          data-zone=\"156349\"\n          async\n          data-cfasync=\"false\"\n          strategy=\"afterInteractive\"\n        />\n      </div>\n    );\n  }\n\n  // Show final redirect page\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n      <div className=\"text-center max-w-md\">\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-white mb-4\">Ready to Redirect!</h2>\n          <p className=\"text-gray-300 mb-6\">Click the button below to visit your destination:</p>\n          <p className=\"text-purple-400 font-mono text-sm mb-6 break-all\">{linkData?.originalUrl}</p>\n          \n          <button\n            onClick={handleRedirect}\n            className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full\"\n          >\n            Visit Link →\n          </button>\n          \n          <Link href=\"/\" className=\"block mt-4 text-purple-400 hover:text-purple-300 transition-colors\">\n            ← Back to GameHub\n          </Link>\n        </div>\n      </div>\n\n      {/* ========== PASTE YOUR BANNER AD SCRIPT HERE (COPY) ========== */}\n\n\n\n      {/* ========== END BANNER AD SCRIPT (COPY) ========== */}\n\n      {/* Montag Ad Network Script */}\n      <Script\n        src=\"https://fpyf8.com/88/tag.min.js\"\n        data-zone=\"156349\"\n        async\n        data-cfasync=\"false\"\n        strategy=\"afterInteractive\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,qBAAqB;IAC7E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;gBACZ,WAAW;YACb,EAAE,OAAM;gBACN,SAAS;gBACT,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,oBAAoB,aAAa,GAAG;QAEzC,MAAM,QAAQ,YAAY;YACxB,aAAa,CAAC;gBACZ,IAAI,QAAQ,GAAG;oBACb,cAAc;oBACd;oBACA,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAU;IAEhC,4EAA4E;IAC5E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,QAAQ,kDAAkD;QAEhF,MAAM,mBAAmB,YAAY;YACnC,iBAAiB,CAAC;gBAChB,IAAI,QAAQ,GAAG;oBACb,cAAc;oBACd,wDAAwD;oBACxD,IAAI,UAAU,aAAa;wBACzB;oBACF;oBACA,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,oBAAoB;QACpB,mDAAmD;QACnD,aAAa;IACf;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,uDAAuD;YACvD,MAAM,QAAQ,WAAW;gBACvB,mBAAmB;YACrB,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAM;IAEnB,MAAM,eAAe;QACnB,IAAI,aAAa,GAAG;YAClB,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU;YACZ,kBAAkB;YAClB,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBAAE,QAAQ;YAAO;YACrD,2BAA2B;YAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,WAAW;QAC7C;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCAAoJ;;;;;;;;;;;;;;;;;IAMrL;IAEA,IAAI,QAAQ;QACV,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;oCAAmF;kDACvG,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAElG,8OAAC;gCAAI,WAAU;;oCAA4B;oCACzB;oCAAU;;;;;;;;;;;;;;;;;;8BAKhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,CAAC,iCACA;sDAAE;0EAEF;;gDAAE;gDAAkB;gDAAU;;;;;;;;oCAIjC,CAAC,kCACA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;0DAGvC,8OAAC;gDAAE,WAAU;;oDAA6B;oDAC/B;oDAAc;;;;;;;;;;;;;kDAM7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,SAAS;0DAER,gCACC,8OAAC;oDAAI,WAAU;8DAOb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,YAAY,kCAAkC;;;;;;;;;;yEAInD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,iKAAiK,EAC3K,YAAY,mCAAmC,IAC/C;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,YAAY,kCAAkC;;;;;;sEAEjD,8OAAC;4DAAE,WAAU;sEACV,YAAY,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;;4CACZ,iCACC;;kEACE,8OAAC;wDAAI,WAAU;kEACZ;;;;;;kEAEH,8OAAC;wDAAE,WAAU;;4DAAsB;4DACH;4DAAU;;;;;;;;6EAI5C;;kEACE,8OAAC;wDAAI,WAAU;kEACZ;;;;;;kEAEH,8OAAC;wDAAE,WAAU;;4DAAuB;4DACL;4DAAc;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,qBAAqB;;;;;;;;;;;;;;;;;;0CAM/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAuC,UAAU;;;;;;;;;;;;;;;;;;;;;;;8BAYpE,8OAAC,8HAAA,CAAA,UAAM;oBACL,KAAI;oBACJ,aAAU;oBACV,KAAK;oBACL,gBAAa;oBACb,UAAS;;;;;;;;;;;;IAIjB;IAEA,2BAA2B;IAC3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC;4BAAE,WAAU;sCAAoD,UAAU;;;;;;sCAE3E,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAqE;;;;;;;;;;;;;;;;;0BAalG,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,aAAU;gBACV,KAAK;gBACL,gBAAa;gBACb,UAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}