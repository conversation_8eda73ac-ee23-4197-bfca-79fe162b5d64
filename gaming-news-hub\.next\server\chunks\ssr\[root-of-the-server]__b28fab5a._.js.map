{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\n\ninterface LinkData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function RedirectPage() {\n  const params = useParams();\n  const shortCode = params.shortCode as string;\n  \n  const [linkData, setLinkData] = useState<LinkData | null>(null);\n  const [countdown, setCountdown] = useState(30); // 30 seconds countdown\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [adClicked, setAdClicked] = useState(false);\n  const [countdownStarted, setCountdownStarted] = useState(false);\n  const [bannerAdsLoaded, setBannerAdsLoaded] = useState(false);\n\n  // Fetch link data\n  useEffect(() => {\n    const fetchLinkData = async () => {\n      try {\n        const response = await fetch(`/api/redirect/${shortCode}`);\n        const data = await response.json();\n        \n        if (response.ok) {\n          setLinkData(data);\n        } else {\n          setError(data.error || 'Link not found');\n        }\n      } catch (err) {\n        setError('Failed to load link data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLinkData();\n  }, [shortCode]);\n\n  // Countdown timer\n  useEffect(() => {\n    if (!countdownStarted || countdown <= 0) return;\n\n    const timer = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          clearInterval(timer);\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [countdownStarted, countdown]);\n\n  // Load banner ads after component mounts\n  useEffect(() => {\n    if (!loading && !error) {\n      // Set a small delay to ensure the page is fully loaded\n      const timer = setTimeout(() => {\n        setBannerAdsLoaded(true);\n        console.log('Banner ads container loaded');\n      }, 2000); // Increased delay to 2 seconds\n      return () => clearTimeout(timer);\n    }\n  }, [loading, error]);\n\n  // Global click listener for ad overlays (Vignette Banner & In-Page Push)\n  useEffect(() => {\n    if (bannerAdsLoaded && !adClicked) {\n      const handleGlobalClick = (event: MouseEvent) => {\n        const target = event.target as HTMLElement;\n\n        // Check if click is on an ad element (common ad class names and attributes)\n        const isAdClick = target.closest('[class*=\"ad\"]') ||\n                         target.closest('[id*=\"ad\"]') ||\n                         target.closest('[class*=\"banner\"]') ||\n                         target.closest('[class*=\"popup\"]') ||\n                         target.closest('[class*=\"overlay\"]') ||\n                         target.hasAttribute('data-ad') ||\n                         target.tagName === 'IFRAME';\n\n        if (isAdClick) {\n          console.log('Ad overlay clicked!', target);\n          handleAdClick();\n        }\n      };\n\n      // Add global click listener\n      document.addEventListener('click', handleGlobalClick, true);\n\n      // Also check for ad elements periodically\n      const checkAds = setInterval(() => {\n        const adElements = document.querySelectorAll('[class*=\"ad\"], [id*=\"ad\"], iframe');\n        if (adElements.length > 0) {\n          console.log('Ad elements detected:', adElements.length);\n        }\n      }, 3000);\n\n      return () => {\n        document.removeEventListener('click', handleGlobalClick, true);\n        clearInterval(checkAds);\n      };\n    }\n  }, [bannerAdsLoaded, adClicked]);\n\n  const handleAdClick = () => {\n    console.log('Ad clicked! Starting 30-second countdown...');\n    setAdClicked(true);\n    setCountdownStarted(true);\n    // Reset countdown to 30 seconds when ad is clicked\n    setCountdown(30);\n  };\n\n  const handleRedirect = () => {\n    if (linkData?.originalUrl) {\n      window.location.href = linkData.originalUrl;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-white text-xl\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center max-w-md\">\n          <div className=\"bg-red-500/20 border border-red-500 rounded-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-red-400 mb-4\">Error</h2>\n            <p className=\"text-gray-300 mb-6\">{error}</p>\n            <Link href=\"/\" className=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\">\n              Go Home\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n        <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n          <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n            🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n          </Link>\n          <div className=\"text-purple-400 font-mono\">\n            {countdownStarted ? `Redirecting in ${countdown}s` : 'Click ad to start'}\n          </div>\n        </div>\n      </nav>\n\n      <div className=\"flex items-center justify-center min-h-[calc(100vh-80px)] p-4\">\n        <div className=\"max-w-2xl w-full\">\n          {/* Ad Section */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n            <h2 className=\"text-2xl font-bold text-white mb-4 text-center\">\n              Please wait while we prepare your link\n            </h2>\n\n            {!countdownStarted && (\n              <div className=\"text-center mb-4\">\n                <p className=\"text-yellow-300 text-sm\">\n                  ⚠️ You must click the advertisement below to start the countdown\n                </p>\n              </div>\n            )}\n\n            {/* Banner Ad Code Container */}\n            <div className=\"bg-white/5 rounded-lg p-6 mb-6 text-center\">\n              <p className=\"text-gray-300 text-sm mb-4\">Advertisement - Click any banner to continue</p>\n              <div\n                id=\"banner-ad-container\"\n                className=\"min-h-[200px] flex items-center justify-center cursor-pointer\"\n                onClick={handleAdClick}\n              >\n                {bannerAdsLoaded ? (\n                  <div className=\"w-full\">\n                    {/* Vignette Banner & In-Page Push ads will appear as overlays */}\n                    <div\n                      className=\"w-full min-h-[200px] bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-lg border border-purple-500/30 flex flex-col items-center justify-center p-8 cursor-pointer hover:from-purple-900/30 hover:to-pink-900/30 transition-all\"\n                      onClick={handleAdClick}\n                    >\n                      <div className=\"text-center\">\n                        <div className=\"text-4xl mb-4\">🎯</div>\n                        <h3 className=\"text-white text-lg font-semibold mb-2\">Advertisement Zone</h3>\n                        <p className=\"text-gray-300 text-sm mb-4\">\n                          Vignette Banner & In-Page Push ads will appear as overlays\n                        </p>\n                        <p className=\"text-purple-300 text-xs mb-4\">\n                          Click here if ads don't appear automatically\n                        </p>\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\"></div>\n                          <div className=\"w-2 h-2 bg-pink-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-gray-400 text-sm text-center mt-4\">\n                      {adClicked ? '✅ Ad clicked! Countdown started' : '👆 Click on any ad popup/overlay to start countdown'}\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"text-gray-400 text-sm\">Loading advertisement scripts...</div>\n                )}\n              </div>\n            </div>\n\n            {/* Countdown Display */}\n            <div className=\"text-center mb-6\">\n              <div className=\"bg-white/5 rounded-lg p-6\">\n                {countdownStarted ? (\n                  <div className=\"text-center\">\n                    <div className=\"bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold\">\n                      {countdown}\n                    </div>\n                    <p className=\"text-purple-300 mb-4\">\n                      ⏳ Please wait {countdown} seconds...\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"text-center\">\n                    <p className=\"text-yellow-300 mb-4\">\n                      ⏳ Click an advertisement above to start the countdown\n                    </p>\n                  </div>\n                )}\n\n                <div className=\"bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold\">\n                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* Final redirect section - only show after countdown */}\n            {countdown <= 0 && countdownStarted && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mt-6\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 text-center\">Link Ready!</h2>\n                <p className=\"text-gray-300 mb-6 text-center\">Click the button below to get your link:</p>\n\n                <button\n                  onClick={handleRedirect}\n                  className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full\"\n                >\n                  Get Your Link\n                </button>\n\n                <Link href=\"/\" className=\"block mt-4 text-purple-400 hover:text-purple-300 transition-colors text-center\">\n                  ← Back to GameHub\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Banner Ad Script 1 */}\n      <Script\n        dangerouslySetInnerHTML={{\n          __html: `\n            console.log('Loading banner ad script 1...');\n            (function(d,z,s){\n              s.src='https://'+d+'/401/'+z;\n              s.onload = function() { console.log('Banner ad script 1 loaded'); };\n              s.onerror = function() { console.log('Banner ad script 1 failed to load'); };\n              try{\n                (document.body||document.documentElement).appendChild(s);\n              }catch(e){\n                console.log('Error appending banner ad script 1:', e);\n              }\n            })('gizokraijaw.net',9550341,document.createElement('script'))\n          `\n        }}\n        strategy=\"beforeInteractive\"\n      />\n\n      {/* Banner Ad Script 2 */}\n      <Script\n        dangerouslySetInnerHTML={{\n          __html: `\n            console.log('Loading banner ad script 2...');\n            (function(d,z,s){\n              s.src='https://'+d+'/400/'+z;\n              s.onload = function() { console.log('Banner ad script 2 loaded'); };\n              s.onerror = function() { console.log('Banner ad script 2 failed to load'); };\n              try{\n                (document.body||document.documentElement).appendChild(s);\n              }catch(e){\n                console.log('Error appending banner ad script 2:', e);\n              }\n            })('vemtoutcheeg.com',9550670,document.createElement('script'))\n          `\n        }}\n        strategy=\"beforeInteractive\"\n      />\n\n      {/* Montag Ad Network Script */}\n      <Script\n        src=\"https://fpyf8.com/88/tag.min.js\"\n        data-zone=\"156349\"\n        async\n        data-cfasync=\"false\"\n        strategy=\"afterInteractive\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,EAAE;oBACf,YAAY;gBACd,OAAO;oBACL,SAAS,KAAK,KAAK,IAAI;gBACzB;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,oBAAoB,aAAa,GAAG;QAEzC,MAAM,QAAQ,YAAY;YACxB,aAAa,CAAC;gBACZ,IAAI,QAAQ,GAAG;oBACb,cAAc;oBACd,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAU;IAEhC,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,uDAAuD;YACvD,MAAM,QAAQ,WAAW;gBACvB,mBAAmB;gBACnB,QAAQ,GAAG,CAAC;YACd,GAAG,OAAO,+BAA+B;YACzC,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAM;IAEnB,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,CAAC,WAAW;YACjC,MAAM,oBAAoB,CAAC;gBACzB,MAAM,SAAS,MAAM,MAAM;gBAE3B,4EAA4E;gBAC5E,MAAM,YAAY,OAAO,OAAO,CAAC,oBAChB,OAAO,OAAO,CAAC,iBACf,OAAO,OAAO,CAAC,wBACf,OAAO,OAAO,CAAC,uBACf,OAAO,OAAO,CAAC,yBACf,OAAO,YAAY,CAAC,cACpB,OAAO,OAAO,KAAK;gBAEpC,IAAI,WAAW;oBACb,QAAQ,GAAG,CAAC,uBAAuB;oBACnC;gBACF;YACF;YAEA,4BAA4B;YAC5B,SAAS,gBAAgB,CAAC,SAAS,mBAAmB;YAEtD,0CAA0C;YAC1C,MAAM,WAAW,YAAY;gBAC3B,MAAM,aAAa,SAAS,gBAAgB,CAAC;gBAC7C,IAAI,WAAW,MAAM,GAAG,GAAG;oBACzB,QAAQ,GAAG,CAAC,yBAAyB,WAAW,MAAM;gBACxD;YACF,GAAG;YAEH,OAAO;gBACL,SAAS,mBAAmB,CAAC,SAAS,mBAAmB;gBACzD,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,oBAAoB;QACpB,mDAAmD;QACnD,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,aAAa;YACzB,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,WAAW;QAC7C;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsF;;;;;;;;;;;;;;;;;;;;;;IAOzH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;gCAAmF;8CACvG,8OAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAElG,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;4BAI9D,CAAC,kCACA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCACC,IAAG;wCACH,WAAU;wCACV,SAAS;kDAER,gCACC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,8OAAC;gEAAE,WAAU;0EAA+B;;;;;;0EAG5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;wEAAkD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAC/F,8OAAC;wEAAI,WAAU;wEAAoD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;;;;;;;;;;;;8DAKvG,8OAAC;oDAAI,WAAU;8DACZ,YAAY,oCAAoC;;;;;;;;;;;iEAIrD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,8OAAC;oDAAE,WAAU;;wDAAuB;wDACnB;wDAAU;;;;;;;;;;;;iEAI7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;sDAMxC,8OAAC;4CAAI,WAAU;sDACZ,mBAAmB,qBAAqB;;;;;;;;;;;;;;;;;4BAQ9C,aAAa,KAAK,kCACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAE9C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAID,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAiF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpH,8OAAC,8HAAA,CAAA,UAAM;gBACL,yBAAyB;oBACvB,QAAQ,CAAC;;;;;;;;;;;;UAYT,CAAC;gBACH;gBACA,UAAS;;;;;;0BAIX,8OAAC,8HAAA,CAAA,UAAM;gBACL,yBAAyB;oBACvB,QAAQ,CAAC;;;;;;;;;;;;UAYT,CAAC;gBACH;gBACA,UAAS;;;;;;0BAIX,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,aAAU;gBACV,KAAK;gBACL,gBAAa;gBACb,UAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}