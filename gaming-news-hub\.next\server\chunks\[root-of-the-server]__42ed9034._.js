module.exports = {

"[project]/.next-internal/server/app/api/redirect/[shortCode]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/urlStore.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Hybrid URL store - uses database in production, memory locally
__turbopack_context__.s({
    "urlStore": (()=>urlStore)
});
// In-memory store for local development
class MemoryUrlStore {
    store = new Map();
    async set(shortCode, data) {
        this.store.set(shortCode, data);
    }
    async get(shortCode) {
        return this.store.get(shortCode);
    }
    async has(shortCode) {
        return this.store.has(shortCode);
    }
    async getAll() {
        return Array.from(this.store.entries()).map(([shortCode, data])=>({
                shortCode,
                ...data
            }));
    }
    async incrementClicks(shortCode) {
        const data = this.store.get(shortCode);
        if (data) {
            data.clicks += 1;
            this.store.set(shortCode, data);
            return true;
        }
        return false;
    }
}
// Database store for production
class DatabaseUrlStore {
    initialized = false;
    async init() {
        if (!this.initialized) {
            const { initDatabase } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await initDatabase();
            this.initialized = true;
        }
    }
    async set(shortCode, data) {
        await this.init();
        const { createShortUrl } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        await createShortUrl(shortCode, data.originalUrl);
    }
    async get(shortCode) {
        await this.init();
        const { getUrlByShortCode } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const record = await getUrlByShortCode(shortCode);
        if (!record) return undefined;
        return {
            originalUrl: record.original_url,
            clicks: record.clicks,
            createdAt: record.created_at
        };
    }
    async has(shortCode) {
        await this.init();
        const { checkShortCodeExists } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        return await checkShortCodeExists(shortCode);
    }
    async getAll() {
        await this.init();
        const { getRecentLinks } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const records = await getRecentLinks();
        return records.map((record)=>({
                shortCode: record.short_code,
                originalUrl: record.original_url,
                clicks: record.clicks,
                createdAt: record.created_at
            }));
    }
    async incrementClicks(shortCode) {
        await this.init();
        try {
            const { incrementClicks } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await incrementClicks(shortCode);
            return true;
        } catch  {
            return false;
        }
    }
}
// Choose store based on environment
function createUrlStore() {
    // Use database if DATABASE_URL is provided, otherwise use memory
    if (process.env.DATABASE_URL) {
        console.log('Using database store');
        return new DatabaseUrlStore();
    } else {
        console.log('Using memory store (local development)');
        return new MemoryUrlStore();
    }
}
const urlStore = createUrlStore();
}}),
"[project]/src/app/api/redirect/[shortCode]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/urlStore.ts [app-route] (ecmascript)");
;
;
async function GET(request, { params }) {
    const { shortCode } = await params;
    const linkData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].get(shortCode);
    if (!linkData) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Link not found'
        }, {
            status: 404
        });
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        originalUrl: linkData.originalUrl,
        clicks: linkData.clicks,
        createdAt: linkData.createdAt.toISOString()
    });
}
async function POST(request, { params }) {
    const { shortCode } = await params;
    const success = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].incrementClicks(shortCode);
    if (!success) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Link not found'
        }, {
            status: 404
        });
    }
    const linkData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].get(shortCode);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        clicks: linkData?.clicks || 0
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__42ed9034._.js.map