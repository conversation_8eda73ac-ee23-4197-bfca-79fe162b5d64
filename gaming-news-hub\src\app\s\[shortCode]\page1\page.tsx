'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function Page1() {
  const params = useParams();
  const router = useRouter();
  const shortCode = params.shortCode as string;

  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showContinue, setShowContinue] = useState(false);

  // Fetch link data
  useEffect(() => {
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        const data = await response.json();

        if (response.ok) {
          setLinkData(data);
        } else {
          setError(data.error || 'Link not found');
        }
      } catch (err) {
        setError('Failed to load link data');
      } finally {
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Show continue button after 20 seconds
  useEffect(() => {
    if (!loading && !error) {
      const timer = setTimeout(() => {
        setShowContinue(true);
      }, 20000); // 20 seconds

      return () => clearTimeout(timer);
    }
  }, [loading, error]);

  const handleContinue = () => {
    router.push(`/s/${shortCode}/page2`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white text-xl">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="bg-red-500/20 border border-red-500 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-red-400 mb-4">Error</h2>
            <p className="text-gray-300 mb-6">{error}</p>
            <Link href="/" className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
              Go Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
            🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
          </Link>
          <div className="text-purple-400 font-mono">
            Preparing your link...
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto p-6">
        {/* Banner Ad 1 */}
        <div className="bg-white/5 rounded-lg p-4 mb-6 text-center">
          <p className="text-gray-400 text-xs mb-2">Advertisement</p>
          <div className="min-h-[120px] bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded flex items-center justify-center">
            <span className="text-gray-400">Banner Ad Zone 1</span>
          </div>
        </div>

        {/* Blog Content */}
        <article className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
          <h1 className="text-3xl font-bold text-white mb-6">
            🎮 The Ultimate Guide to Gaming in 2024
          </h1>

          <div className="prose prose-invert max-w-none">
            <p className="text-gray-300 mb-4 leading-relaxed">
              Gaming has evolved tremendously over the past decade, and 2024 promises to be one of the most exciting years yet for gamers worldwide.
              From cutting-edge graphics to immersive virtual reality experiences, the gaming industry continues to push boundaries and redefine entertainment.
            </p>

            <h2 className="text-2xl font-semibold text-purple-300 mb-4 mt-8">🚀 Latest Gaming Trends</h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              The rise of cloud gaming has made high-quality gaming accessible to more people than ever before. Services like Xbox Game Pass,
              PlayStation Now, and Google Stadia have revolutionized how we access and play games. No longer do you need expensive hardware
              to enjoy the latest AAA titles.
            </p>

            {/* Banner Ad 2 */}
            <div className="bg-white/5 rounded-lg p-4 my-8 text-center">
              <p className="text-gray-400 text-xs mb-2">Advertisement</p>
              <div className="min-h-[120px] bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded flex items-center justify-center">
                <span className="text-gray-400">Banner Ad Zone 2</span>
              </div>
            </div>

            <h2 className="text-2xl font-semibold text-purple-300 mb-4">🎯 Best Gaming Platforms</h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Whether you're a PC master race enthusiast or a console loyalist, there's never been a better time to be a gamer.
              The PlayStation 5 and Xbox Series X/S have brought 4K gaming and ray tracing to the mainstream, while the Nintendo Switch
              continues to dominate the portable gaming market.
            </p>

            <ul className="text-gray-300 mb-6 space-y-2">
              <li>• <strong className="text-purple-300">PC Gaming:</strong> Ultimate customization and performance</li>
              <li>• <strong className="text-purple-300">PlayStation 5:</strong> Exclusive titles and innovative DualSense controller</li>
              <li>• <strong className="text-purple-300">Xbox Series X/S:</strong> Game Pass integration and backward compatibility</li>
              <li>• <strong className="text-purple-300">Nintendo Switch:</strong> Portable gaming perfection</li>
            </ul>

            <h2 className="text-2xl font-semibold text-purple-300 mb-4">🏆 Top Games to Play Right Now</h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              The gaming landscape is filled with incredible titles across all genres. From epic single-player adventures to competitive
              multiplayer experiences, there's something for every type of gamer.
            </p>

            {/* Banner Ad 3 */}
            <div className="bg-white/5 rounded-lg p-4 my-8 text-center">
              <p className="text-gray-400 text-xs mb-2">Advertisement</p>
              <div className="min-h-[120px] bg-gradient-to-r from-green-900/30 to-blue-900/30 rounded flex items-center justify-center">
                <span className="text-gray-400">Banner Ad Zone 3</span>
              </div>
            </div>

            <p className="text-gray-300 mb-4 leading-relaxed">
              Action-adventure games like "The Legend of Zelda: Tears of the Kingdom" and "Spider-Man 2" have set new standards
              for open-world exploration and storytelling. Meanwhile, competitive games like "Valorant" and "Apex Legends"
              continue to evolve with regular updates and seasonal content.
            </p>

            <h2 className="text-2xl font-semibold text-purple-300 mb-4">💡 Gaming Tips for Beginners</h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Starting your gaming journey can be overwhelming with so many options available. Here are some essential tips
              to help you get started and make the most of your gaming experience.
            </p>

            <div className="bg-purple-900/20 rounded-lg p-6 mb-6">
              <h3 className="text-xl font-semibold text-purple-300 mb-3">Essential Gaming Setup</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• Invest in a comfortable gaming chair for long sessions</li>
                <li>• Good headphones or speakers for immersive audio</li>
                <li>• Stable internet connection for online gaming</li>
                <li>• Proper lighting to reduce eye strain</li>
              </ul>
            </div>

            {/* Banner Ad 4 */}
            <div className="bg-white/5 rounded-lg p-4 my-8 text-center">
              <p className="text-gray-400 text-xs mb-2">Advertisement</p>
              <div className="min-h-[120px] bg-gradient-to-r from-pink-900/30 to-purple-900/30 rounded flex items-center justify-center">
                <span className="text-gray-400">Banner Ad Zone 4</span>
              </div>
            </div>

            <h2 className="text-2xl font-semibold text-purple-300 mb-4">🌟 The Future of Gaming</h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              As we look ahead, the future of gaming is incredibly bright. Virtual reality is becoming more accessible,
              artificial intelligence is creating more dynamic game worlds, and cross-platform play is bringing gamers together
              regardless of their chosen platform.
            </p>

            <p className="text-gray-300 mb-8 leading-relaxed">
              The integration of blockchain technology and NFTs in gaming is also creating new opportunities for players
              to truly own their in-game assets. While still in its early stages, this technology could revolutionize
              how we think about digital ownership in games.
            </p>
          </div>
        </article>

        {/* Continue Button - Only shows after 20 seconds */}
        {showContinue && (
          <div className="text-center mb-8">
            <button
              onClick={handleContinue}
              className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg"
            >
              Continue to Next Step →
            </button>
          </div>
        )}
      </div>

      {/* Montag Banner Ad Script */}
      <Script
        dangerouslySetInnerHTML={{
          __html: `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('gizokraijaw.net',9550341,document.createElement('script'))`
        }}
        strategy="afterInteractive"
      />

      {/* PopAds Script */}
      <Script
        dangerouslySetInnerHTML={{
          __html: `
            (function(){var h=window,q="f698b60b6d08d4ddea0dff14beeaa446",f=[["siteId",905*337-585-19+4912789],["minBid",0],["popundersPerIP","0"],["delayBetween",0],["default",false],["defaultPerDay",0],["topmostLayer","auto"]],l=["d3d3LmJldHRlcmFkc3lzdGVtLmNvbS9CcGdEVVUvQS9qdmlzaWJseS5taW4uanM=","ZDJrazBvM2ZyN2VkMDEuY2xvdWRmcm9udC5uZXQvbmV4aWYubWluLmpz"],y=-1,d,x,m=function(){clearTimeout(x);y++;if(l[y]&&!(1778084907000<(new Date).getTime()&&1<y)){d=h.document.createElement("script");d.type="text/javascript";d.async=!0;var o=h.document.getElementsByTagName("script")[0];d.src="https://"+atob(l[y]);d.crossOrigin="anonymous";d.onerror=m;d.onload=function(){clearTimeout(x);h[q.slice(0,16)+q.slice(0,16)]||m()};x=setTimeout(m,5E3);o.parentNode.insertBefore(d,o)}};if(!h[q]){try{Object.freeze(h[q]=f)}catch(e){}m()}})();
          `
        }}
        strategy="afterInteractive"
      />
    </div>
  );
}
