{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/database.ts"], "sourcesContent": ["import { Pool } from 'pg';\n\n// Database connection pool\nlet pool: Pool | null = null;\n\nexport function getPool(): Pool {\n  if (!pool) {\n    pool = new Pool({\n      connectionString: process.env.DATABASE_URL,\n      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,\n    });\n  }\n  return pool;\n}\n\n// Initialize database tables\nexport async function initDatabase() {\n  const client = getPool();\n  \n  try {\n    // Create shortened_links table\n    await client.query(`\n      CREATE TABLE IF NOT EXISTS shortened_links (\n        id SERIAL PRIMARY KEY,\n        short_code VARCHAR(10) UNIQUE NOT NULL,\n        original_url TEXT NOT NULL,\n        clicks INTEGER DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n\n    // Create index for faster lookups\n    await client.query(`\n      CREATE INDEX IF NOT EXISTS idx_short_code ON shortened_links(short_code)\n    `);\n\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Database initialization error:', error);\n    throw error;\n  }\n}\n\n// URL operations\nexport interface UrlRecord {\n  id: number;\n  short_code: string;\n  original_url: string;\n  clicks: number;\n  created_at: Date;\n  updated_at: Date;\n}\n\nexport async function createShortUrl(shortCode: string, originalUrl: string): Promise<UrlRecord> {\n  const client = getPool();\n  \n  const result = await client.query(\n    'INSERT INTO shortened_links (short_code, original_url) VALUES ($1, $2) RETURNING *',\n    [shortCode, originalUrl]\n  );\n  \n  return result.rows[0];\n}\n\nexport async function getUrlByShortCode(shortCode: string): Promise<UrlRecord | null> {\n  const client = getPool();\n  \n  const result = await client.query(\n    'SELECT * FROM shortened_links WHERE short_code = $1',\n    [shortCode]\n  );\n  \n  return result.rows[0] || null;\n}\n\nexport async function incrementClicks(shortCode: string): Promise<number> {\n  const client = getPool();\n  \n  const result = await client.query(\n    'UPDATE shortened_links SET clicks = clicks + 1, updated_at = CURRENT_TIMESTAMP WHERE short_code = $1 RETURNING clicks',\n    [shortCode]\n  );\n  \n  return result.rows[0]?.clicks || 0;\n}\n\nexport async function getRecentLinks(limit: number = 10): Promise<UrlRecord[]> {\n  const client = getPool();\n  \n  const result = await client.query(\n    'SELECT * FROM shortened_links ORDER BY created_at DESC LIMIT $1',\n    [limit]\n  );\n  \n  return result.rows;\n}\n\nexport async function checkShortCodeExists(shortCode: string): Promise<boolean> {\n  const client = getPool();\n  \n  const result = await client.query(\n    'SELECT 1 FROM shortened_links WHERE short_code = $1',\n    [shortCode]\n  );\n  \n  return result.rows.length > 0;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;AAEA,2BAA2B;AAC3B,IAAI,OAAoB;AAEjB,SAAS;IACd,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;YACd,kBAAkB,QAAQ,GAAG,CAAC,YAAY;YAC1C,KAAK,6EAAwE;QAC/E;IACF;IACA,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,SAAS;IAEf,IAAI;QACF,+BAA+B;QAC/B,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;;;;;IASpB,CAAC;QAED,kCAAkC;QAClC,MAAM,OAAO,KAAK,CAAC,CAAC;;IAEpB,CAAC;QAED,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAYO,eAAe,eAAe,SAAiB,EAAE,WAAmB;IACzE,MAAM,SAAS;IAEf,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,sFACA;QAAC;QAAW;KAAY;IAG1B,OAAO,OAAO,IAAI,CAAC,EAAE;AACvB;AAEO,eAAe,kBAAkB,SAAiB;IACvD,MAAM,SAAS;IAEf,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,uDACA;QAAC;KAAU;IAGb,OAAO,OAAO,IAAI,CAAC,EAAE,IAAI;AAC3B;AAEO,eAAe,gBAAgB,SAAiB;IACrD,MAAM,SAAS;IAEf,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,yHACA;QAAC;KAAU;IAGb,OAAO,OAAO,IAAI,CAAC,EAAE,EAAE,UAAU;AACnC;AAEO,eAAe,eAAe,QAAgB,EAAE;IACrD,MAAM,SAAS;IAEf,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,mEACA;QAAC;KAAM;IAGT,OAAO,OAAO,IAAI;AACpB;AAEO,eAAe,qBAAqB,SAAiB;IAC1D,MAAM,SAAS;IAEf,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,uDACA;QAAC;KAAU;IAGb,OAAO,OAAO,IAAI,CAAC,MAAM,GAAG;AAC9B", "debugId": null}}]}