module.exports = {

"[project]/.next-internal/server/app/api/shorten/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/urlStore.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Hybrid URL store - uses database in production, memory locally
__turbopack_context__.s({
    "urlStore": (()=>urlStore)
});
// In-memory store for local development
class MemoryUrlStore {
    store = new Map();
    async set(shortCode, data) {
        this.store.set(shortCode, data);
    }
    async get(shortCode) {
        return this.store.get(shortCode);
    }
    async has(shortCode) {
        return this.store.has(shortCode);
    }
    async getAll() {
        return Array.from(this.store.entries()).map(([shortCode, data])=>({
                shortCode,
                ...data
            }));
    }
    async incrementClicks(shortCode) {
        const data = this.store.get(shortCode);
        if (data) {
            data.clicks += 1;
            this.store.set(shortCode, data);
            return true;
        }
        return false;
    }
}
// Database store for production
class DatabaseUrlStore {
    initialized = false;
    async init() {
        if (!this.initialized) {
            const { initDatabase } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await initDatabase();
            this.initialized = true;
        }
    }
    async set(shortCode, data) {
        await this.init();
        const { createShortUrl } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        await createShortUrl(shortCode, data.originalUrl);
    }
    async get(shortCode) {
        await this.init();
        const { getUrlByShortCode } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const record = await getUrlByShortCode(shortCode);
        if (!record) return undefined;
        return {
            originalUrl: record.original_url,
            clicks: record.clicks,
            createdAt: record.created_at
        };
    }
    async has(shortCode) {
        await this.init();
        const { checkShortCodeExists } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        return await checkShortCodeExists(shortCode);
    }
    async getAll() {
        await this.init();
        const { getRecentLinks } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const records = await getRecentLinks();
        return records.map((record)=>({
                shortCode: record.short_code,
                originalUrl: record.original_url,
                clicks: record.clicks,
                createdAt: record.created_at
            }));
    }
    async incrementClicks(shortCode) {
        await this.init();
        try {
            const { incrementClicks } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await incrementClicks(shortCode);
            return true;
        } catch  {
            return false;
        }
    }
}
// Choose store based on environment
function createUrlStore() {
    // Use database if DATABASE_URL is provided, otherwise use memory
    if (process.env.DATABASE_URL) {
        console.log('Using database store');
        return new DatabaseUrlStore();
    } else {
        console.log('Using memory store (local development)');
        return new MemoryUrlStore();
    }
}
const urlStore = createUrlStore();
}}),
"[project]/src/app/api/shorten/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/urlStore.ts [app-route] (ecmascript)");
;
;
// Generate a random short code
function generateShortCode() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for(let i = 0; i < 6; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
// Validate URL format
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch  {
        return false;
    }
}
async function POST(request) {
    try {
        const { url } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL is required'
            }, {
                status: 400
            });
        }
        if (!isValidUrl(url)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid URL format'
            }, {
                status: 400
            });
        }
        // Generate unique short code
        let shortCode = generateShortCode();
        while(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].has(shortCode)){
            shortCode = generateShortCode();
        }
        // Store in database
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].set(shortCode, {
            originalUrl: url,
            clicks: 0,
            createdAt: new Date()
        });
        const baseUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || request.nextUrl.origin;
        const shortUrl = `${baseUrl}/s/${shortCode}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            shortUrl,
            shortCode,
            originalUrl: url
        });
    } catch (error) {
        console.error('Error shortening URL:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function GET() {
    // Return recent links for the dashboard
    const recentLinks = (await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$urlStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["urlStore"].getAll()).map((data)=>({
            ...data,
            shortUrl: `${("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000'}/s/${data.shortCode}`
        })).sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, 10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        links: recentLinks
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e8c5a271._.js.map