{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/components/DealsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Deal } from '@/lib/dealsParser';\n\ninterface DealCardProps {\n  deal: Deal;\n}\n\nfunction DealCard({ deal }: DealCardProps) {\n  const [imageError, setImageError] = useState(false);\n  \n  const handleImageError = () => {\n    setImageError(true);\n  };\n\n  return (\n    <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all transform hover:scale-105\">\n      {/* Product Image/Thumbnail */}\n      <div className=\"h-32 rounded-lg mb-4 overflow-hidden\">\n        {!imageError && deal.thumbnail.url ? (\n          // eslint-disable-next-line @next/next/no-img-element\n          <img\n            src={deal.thumbnail.url}\n            alt={deal.thumbnail.alt}\n            className=\"w-full h-full object-cover\"\n            onError={handleImageError}\n          />\n        ) : (\n          <div className={`w-full h-full bg-gradient-to-br ${deal.thumbnail.fallback} flex items-center justify-center`}>\n            <span className=\"text-white text-2xl font-semibold\">\n              {deal.emoji} {deal.title.split(' ').slice(0, 2).join(' ')}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <h4 className=\"text-lg font-semibold text-white mb-2 line-clamp-2\">\n        {deal.title}\n      </h4>\n      \n      {/* Pricing */}\n      <div className=\"flex items-center gap-2 mb-2\">\n        <span className=\"text-2xl font-bold text-green-400\">\n          ${deal.salePrice.toFixed(2)}\n        </span>\n        <span className=\"text-gray-400 line-through\">\n          ${deal.originalPrice.toFixed(2)}\n        </span>\n      </div>\n      \n      {/* Discount Badge */}\n      <div className=\"flex items-center justify-between\">\n        <span className=\"bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\">\n          {deal.discountPercentage}% OFF\n        </span>\n        \n        {/* Buy Button */}\n        <a\n          href={deal.productUrl}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded text-sm font-semibold hover:from-purple-700 hover:to-pink-700 transition-all\"\n        >\n          Buy Now\n        </a>\n      </div>\n    </div>\n  );\n}\n\ninterface DealsSectionProps {\n  className?: string;\n  id?: string;\n}\n\nexport default function DealsSection({ className = '', id }: DealsSectionProps) {\n  const [deals, setDeals] = useState<Deal[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchDeals();\n  }, []);\n\n  const fetchDeals = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/deals');\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch deals');\n      }\n      \n      const data = await response.json();\n      setDeals(data.deals || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load deals');\n      console.error('Error fetching deals:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>\n        <div className=\"max-w-7xl mx-auto\">\n          <h3 className=\"text-3xl font-bold text-white mb-8 text-center\">Hot Gaming Deals</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[...Array(8)].map((_, i) => (\n              <div key={i} className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 animate-pulse\">\n                <div className=\"h-32 bg-gray-600 rounded-lg mb-4\"></div>\n                <div className=\"h-4 bg-gray-600 rounded mb-2\"></div>\n                <div className=\"h-6 bg-gray-600 rounded mb-2 w-3/4\"></div>\n                <div className=\"h-4 bg-gray-600 rounded w-1/2\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h3 className=\"text-3xl font-bold text-white mb-8\">Hot Gaming Deals</h3>\n          <div className=\"bg-red-500/20 border border-red-500/30 rounded-lg p-6\">\n            <p className=\"text-red-300 mb-4\">Failed to load deals: {error}</p>\n            <button\n              onClick={fetchDeals}\n              className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (deals.length === 0) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h3 className=\"text-3xl font-bold text-white mb-8\">Hot Gaming Deals</h3>\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">🛍️</div>\n              <h4 className=\"text-xl font-semibold text-white mb-2\">No Gaming Deals Yet</h4>\n              <p className=\"text-gray-300 mb-4\">\n                Add gaming deals through environment variables to see them here.\n              </p>\n              <div className=\"bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-left\">\n                <p className=\"text-purple-300 text-sm font-mono\">\n                  DEAL_1=Product Title|Original Price|Sale Price|Product URL\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h3 className=\"text-3xl font-bold text-white\">Hot Gaming Deals</h3>\n          <button\n            onClick={fetchDeals}\n            className=\"text-purple-400 hover:text-purple-300 text-sm transition-colors\"\n          >\n            🔄 Refresh Deals\n          </button>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {deals.map((deal) => (\n            <DealCard key={deal.id} deal={deal} />\n          ))}\n        </div>\n        \n        {deals.length > 0 && (\n          <div className=\"text-center mt-8\">\n            <p className=\"text-gray-400 text-sm\">\n              Showing {deals.length} amazing deals • Updated automatically\n            </p>\n          </div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,SAAS,SAAS,EAAE,IAAI,EAAiB;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,CAAC,cAAc,KAAK,SAAS,CAAC,GAAG,GAChC,qDAAqD;8BACrD,8OAAC;oBACC,KAAK,KAAK,SAAS,CAAC,GAAG;oBACvB,KAAK,KAAK,SAAS,CAAC,GAAG;oBACvB,WAAU;oBACV,SAAS;;;;;yCAGX,8OAAC;oBAAI,WAAW,CAAC,gCAAgC,EAAE,KAAK,SAAS,CAAC,QAAQ,CAAC,iCAAiC,CAAC;8BAC3G,cAAA,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK;4BAAC;4BAAE,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;0BAO7D,8OAAC;gBAAG,WAAU;0BACX,KAAK,KAAK;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;4BAAoC;4BAChD,KAAK,SAAS,CAAC,OAAO,CAAC;;;;;;;kCAE3B,8OAAC;wBAAK,WAAU;;4BAA6B;4BACzC,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;4BACb,KAAK,kBAAkB;4BAAC;;;;;;;kCAI3B,8OAAC;wBACC,MAAM,KAAK,UAAU;wBACrB,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;AAOe,SAAS,aAAa,EAAE,YAAY,EAAE,EAAE,EAAE,EAAqB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;sBAC/E,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAJP;;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;sBAC/E,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAoB;oCAAuB;;;;;;;0CACxD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;sBAC/E,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS/D;IAEA,qBACE,8OAAC;QAAQ,IAAI;QAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAuB,MAAM;2BAAf,KAAK,EAAE;;;;;;;;;;gBAIzB,MAAM,MAAM,GAAG,mBACd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/components/NewsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { NewsArticle } from '@/lib/newsParser';\n\ninterface NewsCardProps {\n  article: NewsArticle;\n}\n\nfunction NewsCard({ article }: NewsCardProps) {\n  const handleClick = () => {\n    if (article.articleUrl) {\n      window.open(article.articleUrl, '_blank', 'noopener,noreferrer');\n    }\n  };\n\n  return (\n    <div \n      className={`bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-white/20 transition-all transform hover:scale-105 ${\n        article.articleUrl ? 'cursor-pointer' : ''\n      }`}\n      onClick={handleClick}\n    >\n      {/* News Image/Header */}\n      <div className={`h-48 bg-gradient-to-br ${article.gradient} flex items-center justify-center`}>\n        <span className=\"text-white text-lg font-semibold\">\n          {article.emoji} {article.category}\n        </span>\n      </div>\n      \n      {/* News Content */}\n      <div className=\"p-6\">\n        <h4 className=\"text-xl font-semibold text-white mb-2 line-clamp-2\">\n          {article.title}\n        </h4>\n        <p className=\"text-gray-300 mb-4 line-clamp-3\">\n          {article.description}\n        </p>\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-purple-400 text-sm\">\n            {article.timeAgo}\n          </span>\n          {article.articleUrl && (\n            <span className=\"text-purple-300 text-xs hover:text-purple-200 transition-colors\">\n              Read more →\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface NewsSectionProps {\n  className?: string;\n  id?: string;\n}\n\nexport default function NewsSection({ className = '', id }: NewsSectionProps) {\n  const [news, setNews] = useState<NewsArticle[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchNews();\n  }, []);\n\n  const fetchNews = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/news');\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch news');\n      }\n      \n      const data = await response.json();\n      setNews(data.news || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load news');\n      console.error('Error fetching news:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>\n        <div className=\"max-w-7xl mx-auto\">\n          <h3 className=\"text-3xl font-bold text-white mb-8 text-center\">Latest Gaming News</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden animate-pulse\">\n                <div className=\"h-48 bg-gray-600\"></div>\n                <div className=\"p-6\">\n                  <div className=\"h-6 bg-gray-600 rounded mb-2\"></div>\n                  <div className=\"h-4 bg-gray-600 rounded mb-2\"></div>\n                  <div className=\"h-4 bg-gray-600 rounded mb-4 w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-600 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h3 className=\"text-3xl font-bold text-white mb-8\">Latest Gaming News</h3>\n          <div className=\"bg-red-500/20 border border-red-500/30 rounded-lg p-6\">\n            <p className=\"text-red-300 mb-4\">Failed to load news: {error}</p>\n            <button\n              onClick={fetchNews}\n              className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (news.length === 0) {\n    return (\n      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h3 className=\"text-3xl font-bold text-white mb-8\">Latest Gaming News</h3>\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">📰</div>\n              <h4 className=\"text-xl font-semibold text-white mb-2\">No News Articles Yet</h4>\n              <p className=\"text-gray-300 mb-4\">\n                Add gaming news through environment variables to see them here.\n              </p>\n              <div className=\"bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-left\">\n                <p className=\"text-purple-300 text-sm font-mono\">\n                  NEWS_1=Game Title|Description|Category|Time|URL\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h3 className=\"text-3xl font-bold text-white\">Latest Gaming News</h3>\n          <button\n            onClick={fetchNews}\n            className=\"text-purple-400 hover:text-purple-300 text-sm transition-colors\"\n          >\n            🔄 Refresh News\n          </button>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {news.map((article) => (\n            <NewsCard key={article.id} article={article} />\n          ))}\n        </div>\n        \n        {news.length > 0 && (\n          <div className=\"text-center mt-8\">\n            <p className=\"text-gray-400 text-sm\">\n              Showing {news.length} latest articles • Updated automatically\n            </p>\n          </div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,SAAS,SAAS,EAAE,OAAO,EAAiB;IAC1C,MAAM,cAAc;QAClB,IAAI,QAAQ,UAAU,EAAE;YACtB,OAAO,IAAI,CAAC,QAAQ,UAAU,EAAE,UAAU;QAC5C;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,mHAAmH,EAC7H,QAAQ,UAAU,GAAG,mBAAmB,IACxC;QACF,SAAS;;0BAGT,8OAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,QAAQ,QAAQ,CAAC,iCAAiC,CAAC;0BAC3F,cAAA,8OAAC;oBAAK,WAAU;;wBACb,QAAQ,KAAK;wBAAC;wBAAE,QAAQ,QAAQ;;;;;;;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,8OAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAEtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,QAAQ,OAAO;;;;;;4BAEjB,QAAQ,UAAU,kBACjB,8OAAC;gCAAK,WAAU;0CAAkE;;;;;;;;;;;;;;;;;;;;;;;;AAQ9F;AAOe,SAAS,YAAY,EAAE,YAAY,EAAE,EAAE,EAAE,EAAoB;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,KAAK,IAAI,IAAI,EAAE;QACzB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;sBACnE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BANT;;;;;;;;;;;;;;;;;;;;;IActB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;sBACnE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAoB;oCAAsB;;;;;;;0CACvD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,8OAAC;YAAQ,IAAI;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;sBACnE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS/D;IAEA,qBACE,8OAAC;QAAQ,IAAI;QAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;kBACnE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,wBACT,8OAAC;4BAA0B,SAAS;2BAArB,QAAQ,EAAE;;;;;;;;;;gBAI5B,KAAK,MAAM,GAAG,mBACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,KAAK,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport { useState, useEffect } from \"react\";\nimport DealsSection from \"@/components/DealsSection\";\nimport NewsSection from \"@/components/NewsSection\";\n\ninterface ShortenedLink {\n  shortCode: string;\n  originalUrl: string;\n  shortUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function Home() {\n  const [url, setUrl] = useState('');\n  const [shortenedUrl, setShortenedUrl] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [recentLinks, setRecentLinks] = useState<ShortenedLink[]>([]);\n  const [copied, setCopied] = useState(false);\n\n  // Fetch recent links on component mount\n  useEffect(() => {\n    fetchRecentLinks();\n  }, []);\n\n  const fetchRecentLinks = async () => {\n    try {\n      const response = await fetch('/api/shorten');\n      if (response.ok) {\n        const data = await response.json();\n        setRecentLinks(data.links);\n      }\n    } catch (error) {\n      console.error('Error fetching recent links:', error);\n    }\n  };\n\n  const handleShorten = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!url.trim()) return;\n\n    setIsLoading(true);\n    setError('');\n    setShortenedUrl('');\n\n    try {\n      const response = await fetch('/api/shorten', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url: url.trim() }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to shorten URL');\n      }\n\n      setShortenedUrl(data.shortUrl);\n      setUrl('');\n      // Refresh recent links\n      fetchRecentLinks();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(shortenedUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Navigation */}\n      <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n                🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n              </Link>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-4\">\n                <Link href=\"#news\" className=\"text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  News\n                </Link>\n                <Link href=\"#deals\" className=\"text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Deals\n                </Link>\n                <Link href=\"#shortener\" className=\"text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Link Shortener\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n            Your Ultimate <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">Gaming Hub</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8 max-w-3xl mx-auto\">\n            Stay updated with the latest gaming news, discover amazing deals on gaming products, and shorten your links with our powerful tools.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button\n              onClick={() => document.getElementById('news')?.scrollIntoView({ behavior: 'smooth' })}\n              className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105\"\n            >\n              Explore News\n            </button>\n            <button\n              onClick={() => document.getElementById('deals')?.scrollIntoView({ behavior: 'smooth' })}\n              className=\"border border-purple-500 text-purple-300 px-8 py-3 rounded-lg font-semibold hover:bg-purple-500/10 transition-all\"\n            >\n              View Deals\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Gaming News Section - Dynamic from Environment Variables */}\n      <NewsSection id=\"news\" />\n\n      {/* Gaming Deals Section - Dynamic from Environment Variables */}\n      <DealsSection id=\"deals\" />\n\n      {/* Link Shortener Section */}\n      <section id=\"shortener\" className=\"py-16 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h3 className=\"text-3xl font-bold text-white mb-8\">Link Shortener</h3>\n          <p className=\"text-gray-300 mb-8\">Shorten your gaming links and track their performance</p>\n\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8\">\n            <form onSubmit={handleShorten} className=\"mb-6\">\n              <div className=\"flex flex-col sm:flex-row gap-4 mb-4\">\n                <input\n                  type=\"url\"\n                  value={url}\n                  onChange={(e) => setUrl(e.target.value)}\n                  placeholder=\"Enter your long URL here...\"\n                  className=\"flex-1 px-4 py-3 rounded-lg bg-white/20 border border-purple-500/30 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                  required\n                />\n                <button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isLoading ? 'Shortening...' : 'Shorten'}\n                </button>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-500/20 border border-red-500/30 rounded-lg p-3 mb-4\">\n                  <p className=\"text-red-300 text-sm\">{error}</p>\n                </div>\n              )}\n\n              {shortenedUrl && (\n                <div className=\"bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-4\">\n                  <p className=\"text-green-300 text-sm mb-2\">Your shortened URL:</p>\n                  <div className=\"flex items-center gap-2\">\n                    <input\n                      type=\"text\"\n                      value={shortenedUrl}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 bg-white/10 border border-green-500/30 rounded text-white font-mono text-sm\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={copyToClipboard}\n                      className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors\"\n                    >\n                      {copied ? 'Copied!' : 'Copy'}\n                    </button>\n                  </div>\n                </div>\n              )}\n            </form>\n\n            <div className=\"text-left\">\n              <h4 className=\"text-white font-semibold mb-4\">Recent Links</h4>\n              <div className=\"space-y-3\">\n                {recentLinks.length > 0 ? (\n                  recentLinks.map((link) => (\n                    <div key={link.shortCode} className=\"flex items-center justify-between bg-white/5 rounded-lg p-4\">\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-purple-400 font-mono\">{link.shortUrl.replace('http://localhost:3000', 'gamehub.ly')}</p>\n                        <p className=\"text-gray-400 text-sm truncate\">{link.originalUrl}</p>\n                      </div>\n                      <div className=\"text-right ml-4\">\n                        <p className=\"text-white font-semibold\">{link.clicks.toLocaleString()} clicks</p>\n                        <p className=\"text-gray-400 text-sm\">\n                          {new Date(link.createdAt).toLocaleDateString()}\n                        </p>\n                      </div>\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"bg-white/5 rounded-lg p-4 text-center\">\n                    <p className=\"text-gray-400\">No links shortened yet. Create your first short link above!</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-black/40 border-t border-purple-500/20 py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <p className=\"text-gray-400\">© 2024 GameHub. Built with Next.js and Tailwind CSS.</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,KAAK;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAChB,IAAI,CAAC,IAAI,IAAI,IAAI;QAEjB,aAAa;QACb,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK,IAAI,IAAI;gBAAG;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,gBAAgB,KAAK,QAAQ;YAC7B,OAAO;YACP,uBAAuB;YACvB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IACA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;wCAAmF;sDACvG,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;;;;;;0CAGpG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAA4F;;;;;;sDAGzH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA4F;;;;;;sDAG1H,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAA4F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxI,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAiD;8CAC/C,8OAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAE7G,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,SAAS,cAAc,CAAC,SAAS,eAAe;4CAAE,UAAU;wCAAS;oCACpF,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,SAAS,cAAc,CAAC,UAAU,eAAe;4CAAE,UAAU;wCAAS;oCACrF,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,iIAAA,CAAA,UAAW;gBAAC,IAAG;;;;;;0BAGhB,8OAAC,kIAAA,CAAA,UAAY;gBAAC,IAAG;;;;;;0BAGjB,8OAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAElC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,UAAU;oCAAe,WAAU;;sDACvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oDACtC,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;8DAEV,8OAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,YAAY,kBAAkB;;;;;;;;;;;;wCAIlC,uBACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;wCAIxC,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,SAAS;4DACT,WAAU;sEAET,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,qBACf,8OAAC;oDAAyB,WAAU;;sEAClC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAA6B,KAAK,QAAQ,CAAC,OAAO,CAAC,yBAAyB;;;;;;8EACzF,8OAAC;oEAAE,WAAU;8EAAkC,KAAK,WAAW;;;;;;;;;;;;sEAEjE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;wEAA4B,KAAK,MAAM,CAAC,cAAc;wEAAG;;;;;;;8EACtE,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;mDARxC,KAAK,SAAS;;;;0EAc1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}]}