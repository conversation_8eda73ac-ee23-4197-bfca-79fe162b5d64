{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/newsParser.ts"], "sourcesContent": ["export interface NewsArticle {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  timeAgo: string;\n  imageUrl?: string;\n  articleUrl?: string;\n  emoji: string;\n  gradient: string;\n}\n\n// Get news emoji based on category\nfunction getNewsEmoji(category: string): string {\n  const categoryLower = category.toLowerCase();\n  \n  if (categoryLower.includes('release') || categoryLower.includes('launch')) return '🎮';\n  if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return '🏆';\n  if (categoryLower.includes('update') || categoryLower.includes('patch')) return '🔥';\n  if (categoryLower.includes('review') || categoryLower.includes('rating')) return '⭐';\n  if (categoryLower.includes('news') || categoryLower.includes('announcement')) return '📰';\n  if (categoryLower.includes('trailer') || categoryLower.includes('video')) return '🎬';\n  if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return '🧪';\n  if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return '📦';\n  if (categoryLower.includes('sale') || categoryLower.includes('discount')) return '💰';\n  if (categoryLower.includes('hardware') || categoryLower.includes('console')) return '🖥️';\n  \n  return '🎮'; // Default gaming emoji\n}\n\n// Get gradient based on category\nfunction getNewsGradient(category: string): string {\n  const categoryLower = category.toLowerCase();\n  \n  if (categoryLower.includes('release') || categoryLower.includes('launch')) return 'from-blue-500 to-purple-600';\n  if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return 'from-green-500 to-blue-600';\n  if (categoryLower.includes('update') || categoryLower.includes('patch')) return 'from-red-500 to-pink-600';\n  if (categoryLower.includes('review') || categoryLower.includes('rating')) return 'from-yellow-500 to-orange-600';\n  if (categoryLower.includes('news') || categoryLower.includes('announcement')) return 'from-purple-500 to-pink-600';\n  if (categoryLower.includes('trailer') || categoryLower.includes('video')) return 'from-cyan-500 to-blue-600';\n  if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return 'from-orange-500 to-red-600';\n  if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return 'from-teal-500 to-green-600';\n  if (categoryLower.includes('sale') || categoryLower.includes('discount')) return 'from-green-600 to-emerald-600';\n  if (categoryLower.includes('hardware') || categoryLower.includes('console')) return 'from-gray-500 to-slate-600';\n  \n  return 'from-blue-500 to-purple-600'; // Default gradient\n}\n\n// Parse news string format: TITLE|DESCRIPTION|CATEGORY|TIME_AGO|ARTICLE_URL(optional)\nfunction parseNewsString(newsString: string, newsId: string): NewsArticle | null {\n  try {\n    const parts = newsString.split('|');\n    if (parts.length < 4) {\n      console.warn(`Invalid news format for ${newsId}: ${newsString}`);\n      return null;\n    }\n\n    const [title, description, category, timeAgo, articleUrl] = parts;\n\n    return {\n      id: newsId,\n      title: title.trim(),\n      description: description.trim(),\n      category: category.trim(),\n      timeAgo: timeAgo.trim(),\n      articleUrl: articleUrl?.trim() || undefined,\n      emoji: getNewsEmoji(category),\n      gradient: getNewsGradient(category)\n    };\n  } catch (error) {\n    console.error(`Error parsing news ${newsId}:`, error);\n    return null;\n  }\n}\n\n// Get all news from environment variables\nexport function getNewsFromEnv(): NewsArticle[] {\n  const news: NewsArticle[] = [];\n  \n  // Look for environment variables starting with NEWS_\n  const envVars = process.env;\n  const newsKeys = Object.keys(envVars)\n    .filter(key => key.startsWith('NEWS_'))\n    .sort((a, b) => {\n      // Sort by number: NEWS_1, NEWS_2, etc.\n      const numA = parseInt(a.replace('NEWS_', ''));\n      const numB = parseInt(b.replace('NEWS_', ''));\n      return numA - numB;\n    });\n\n  for (const key of newsKeys) {\n    const newsString = envVars[key];\n    if (!newsString) continue;\n\n    const parsedNews = parseNewsString(newsString, key);\n    if (parsedNews) {\n      news.push(parsedNews);\n    }\n  }\n\n  return news;\n}\n\n// No default news - site will only show content from environment variables\n"], "names": [], "mappings": ";;;AAYA,mCAAmC;AACnC,SAAS,aAAa,QAAgB;IACpC,MAAM,gBAAgB,SAAS,WAAW;IAE1C,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,WAAW,OAAO;IAClF,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,eAAe,OAAO;IACtF,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,UAAU,OAAO;IAChF,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,WAAW,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,iBAAiB,OAAO;IACrF,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,UAAU,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,UAAU,OAAO;IAC9E,IAAI,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,cAAc,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,aAAa,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,YAAY,OAAO;IAEpF,OAAO,MAAM,uBAAuB;AACtC;AAEA,iCAAiC;AACjC,SAAS,gBAAgB,QAAgB;IACvC,MAAM,gBAAgB,SAAS,WAAW;IAE1C,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,WAAW,OAAO;IAClF,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,eAAe,OAAO;IACtF,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,UAAU,OAAO;IAChF,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,WAAW,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,iBAAiB,OAAO;IACrF,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,UAAU,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,UAAU,OAAO;IAC9E,IAAI,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,cAAc,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,aAAa,OAAO;IACjF,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,YAAY,OAAO;IAEpF,OAAO,+BAA+B,mBAAmB;AAC3D;AAEA,sFAAsF;AACtF,SAAS,gBAAgB,UAAkB,EAAE,MAAc;IACzD,IAAI;QACF,MAAM,QAAQ,WAAW,KAAK,CAAC;QAC/B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO,EAAE,EAAE,YAAY;YAC/D,OAAO;QACT;QAEA,MAAM,CAAC,OAAO,aAAa,UAAU,SAAS,WAAW,GAAG;QAE5D,OAAO;YACL,IAAI;YACJ,OAAO,MAAM,IAAI;YACjB,aAAa,YAAY,IAAI;YAC7B,UAAU,SAAS,IAAI;YACvB,SAAS,QAAQ,IAAI;YACrB,YAAY,YAAY,UAAU;YAClC,OAAO,aAAa;YACpB,UAAU,gBAAgB;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC,EAAE;QAC/C,OAAO;IACT;AACF;AAGO,SAAS;IACd,MAAM,OAAsB,EAAE;IAE9B,qDAAqD;IACrD,MAAM,UAAU,QAAQ,GAAG;IAC3B,MAAM,WAAW,OAAO,IAAI,CAAC,SAC1B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,UAC7B,IAAI,CAAC,CAAC,GAAG;QACR,uCAAuC;QACvC,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,OAAO,OAAO;IAChB;IAEF,KAAK,MAAM,OAAO,SAAU;QAC1B,MAAM,aAAa,OAAO,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY;QAEjB,MAAM,aAAa,gBAAgB,YAAY;QAC/C,IAAI,YAAY;YACd,KAAK,IAAI,CAAC;QACZ;IACF;IAEA,OAAO;AACT,EAEA,2EAA2E", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/api/news/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getNewsFromEnv } from '@/lib/newsParser';\n\nexport async function GET() {\n  try {\n    const news = getNewsFromEnv();\n    return NextResponse.json({ news });\n  } catch (error) {\n    console.error('Error fetching news:', error);\n    return NextResponse.json({ news: [] });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM,EAAE;QAAC;IACtC;AACF", "debugId": null}}]}