// Hybrid URL store - uses database in production, memory locally
export interface UrlData {
  originalUrl: string;
  clicks: number;
  createdAt: Date;
}

// In-memory store for local development
class MemoryUrlStore {
  private store = new Map<string, UrlData>();

  async set(shortCode: string, data: UrlData): Promise<void> {
    this.store.set(shortCode, data);
  }

  async get(shortCode: string): Promise<UrlData | undefined> {
    return this.store.get(shortCode);
  }

  async has(shortCode: string): Promise<boolean> {
    return this.store.has(shortCode);
  }

  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {
    return Array.from(this.store.entries()).map(([shortCode, data]) => ({
      shortCode,
      ...data
    }));
  }

  async incrementClicks(shortCode: string): Promise<boolean> {
    const data = this.store.get(shortCode);
    if (data) {
      data.clicks += 1;
      this.store.set(shortCode, data);
      return true;
    }
    return false;
  }
}

// Database store for production
class DatabaseUrlStore {
  private initialized = false;

  async init() {
    if (!this.initialized) {
      const { initDatabase } = await import('./database');
      await initDatabase();
      this.initialized = true;
    }
  }

  async set(shortCode: string, data: UrlData): Promise<void> {
    await this.init();
    const { createShortUrl } = await import('./database');
    await createShortUrl(shortCode, data.originalUrl);
  }

  async get(shortCode: string): Promise<UrlData | undefined> {
    await this.init();
    const { getUrlByShortCode } = await import('./database');
    const record = await getUrlByShortCode(shortCode);
    if (!record) return undefined;

    return {
      originalUrl: record.original_url,
      clicks: record.clicks,
      createdAt: record.created_at
    };
  }

  async has(shortCode: string): Promise<boolean> {
    await this.init();
    const { checkShortCodeExists } = await import('./database');
    return await checkShortCodeExists(shortCode);
  }

  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {
    await this.init();
    const { getRecentLinks } = await import('./database');
    const records = await getRecentLinks();

    return records.map(record => ({
      shortCode: record.short_code,
      originalUrl: record.original_url,
      clicks: record.clicks,
      createdAt: record.created_at
    }));
  }

  async incrementClicks(shortCode: string): Promise<boolean> {
    await this.init();
    try {
      const { incrementClicks } = await import('./database');
      await incrementClicks(shortCode);
      return true;
    } catch {
      return false;
    }
  }
}

// Choose store based on environment
function createUrlStore() {
  // Use database if DATABASE_URL is provided, otherwise use memory
  if (process.env.DATABASE_URL) {
    console.log('Using database store');
    return new DatabaseUrlStore();
  } else {
    console.log('Using memory store (local development)');
    return new MemoryUrlStore();
  }
}

// Export a singleton instance
export const urlStore = createUrlStore();
