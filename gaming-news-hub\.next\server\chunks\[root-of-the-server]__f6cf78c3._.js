module.exports = {

"[project]/.next-internal/server/app/api/news/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/newsParser.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getNewsFromEnv": (()=>getNewsFromEnv)
});
// Get news emoji based on category
function getNewsEmoji(category) {
    const categoryLower = category.toLowerCase();
    if (categoryLower.includes('release') || categoryLower.includes('launch')) return '🎮';
    if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return '🏆';
    if (categoryLower.includes('update') || categoryLower.includes('patch')) return '🔥';
    if (categoryLower.includes('review') || categoryLower.includes('rating')) return '⭐';
    if (categoryLower.includes('news') || categoryLower.includes('announcement')) return '📰';
    if (categoryLower.includes('trailer') || categoryLower.includes('video')) return '🎬';
    if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return '🧪';
    if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return '📦';
    if (categoryLower.includes('sale') || categoryLower.includes('discount')) return '💰';
    if (categoryLower.includes('hardware') || categoryLower.includes('console')) return '🖥️';
    return '🎮'; // Default gaming emoji
}
// Get gradient based on category
function getNewsGradient(category) {
    const categoryLower = category.toLowerCase();
    if (categoryLower.includes('release') || categoryLower.includes('launch')) return 'from-blue-500 to-purple-600';
    if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return 'from-green-500 to-blue-600';
    if (categoryLower.includes('update') || categoryLower.includes('patch')) return 'from-red-500 to-pink-600';
    if (categoryLower.includes('review') || categoryLower.includes('rating')) return 'from-yellow-500 to-orange-600';
    if (categoryLower.includes('news') || categoryLower.includes('announcement')) return 'from-purple-500 to-pink-600';
    if (categoryLower.includes('trailer') || categoryLower.includes('video')) return 'from-cyan-500 to-blue-600';
    if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return 'from-orange-500 to-red-600';
    if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return 'from-teal-500 to-green-600';
    if (categoryLower.includes('sale') || categoryLower.includes('discount')) return 'from-green-600 to-emerald-600';
    if (categoryLower.includes('hardware') || categoryLower.includes('console')) return 'from-gray-500 to-slate-600';
    return 'from-blue-500 to-purple-600'; // Default gradient
}
// Parse news string format: TITLE|DESCRIPTION|CATEGORY|TIME_AGO|ARTICLE_URL(optional)
function parseNewsString(newsString, newsId) {
    try {
        const parts = newsString.split('|');
        if (parts.length < 4) {
            console.warn(`Invalid news format for ${newsId}: ${newsString}`);
            return null;
        }
        const [title, description, category, timeAgo, articleUrl] = parts;
        return {
            id: newsId,
            title: title.trim(),
            description: description.trim(),
            category: category.trim(),
            timeAgo: timeAgo.trim(),
            articleUrl: articleUrl?.trim() || undefined,
            emoji: getNewsEmoji(category),
            gradient: getNewsGradient(category)
        };
    } catch (error) {
        console.error(`Error parsing news ${newsId}:`, error);
        return null;
    }
}
function getNewsFromEnv() {
    const news = [];
    // Look for environment variables starting with NEWS_
    const envVars = process.env;
    const newsKeys = Object.keys(envVars).filter((key)=>key.startsWith('NEWS_')).sort((a, b)=>{
        // Sort by number: NEWS_1, NEWS_2, etc.
        const numA = parseInt(a.replace('NEWS_', ''));
        const numB = parseInt(b.replace('NEWS_', ''));
        return numA - numB;
    });
    for (const key of newsKeys){
        const newsString = envVars[key];
        if (!newsString) continue;
        const parsedNews = parseNewsString(newsString, key);
        if (parsedNews) {
            news.push(parsedNews);
        }
    }
    return news;
} // No default news - site will only show content from environment variables
}}),
"[project]/src/app/api/news/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$newsParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/newsParser.ts [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        const news = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$newsParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNewsFromEnv"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            news
        });
    } catch (error) {
        console.error('Error fetching news:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            news: []
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f6cf78c3._.js.map