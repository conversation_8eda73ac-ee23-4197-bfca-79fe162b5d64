'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function RedirectPage() {
  const params = useParams();
  const shortCode = params.shortCode as string;

  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [countdown, setCountdown] = useState(30); // 30 seconds countdown
  const [showAd, setShowAd] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adClicked, setAdClicked] = useState(false);
  const [countdownStarted, setCountdownStarted] = useState(false);
  const [fallbackTimer, setFallbackTimer] = useState(50); // 50 second fallback
  const [bannerAdsLoaded, setBannerAdsLoaded] = useState(false);
  const [montageAdsLoaded, setMontageAdsLoaded] = useState(false);

  useEffect(() => {
    // Fetch the original URL
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        if (!response.ok) {
          throw new Error('Link not found');
        }
        const data = await response.json();
        setLinkData(data);
        setLoading(false);
      } catch {
        setError('Link not found or expired');
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Handle countdown only after ad is clicked
  useEffect(() => {
    if (!countdownStarted || countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          handleRedirect();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdownStarted, countdown]);

  // Handle fallback timer (50 seconds) - allows redirect if no ad interaction
  useEffect(() => {
    if (countdownStarted) return; // Don't run fallback if countdown already started

    const fallbackInterval = setInterval(() => {
      setFallbackTimer((prev) => {
        if (prev <= 1) {
          clearInterval(fallbackInterval);
          // If no ad interaction after 50 seconds, allow redirect
          if (linkData?.originalUrl) {
            handleRedirect();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(fallbackInterval);
  }, [countdownStarted, linkData]);

  const handleAdClick = () => {
    console.log('Ad clicked! Starting 30-second countdown...');
    setAdClicked(true);
    setCountdownStarted(true);
    // Reset countdown to 30 seconds when ad is clicked
    setCountdown(30);
  };

  // Load banner ads after component mounts
  useEffect(() => {
    if (!loading && !error) {
      // Set a small delay to ensure the page is fully loaded
      const timer = setTimeout(() => {
        setBannerAdsLoaded(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [loading, error]);

  const handleSkipAd = () => {
    if (countdown <= 0) {
      setShowAd(false);
    }
  };

  const handleRedirect = () => {
    if (linkData) {
      // Track the click
      fetch(`/api/redirect/${shortCode}`, { method: 'POST' });
      // Redirect to original URL
      window.location.href = linkData.originalUrl;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">404</h1>
          <p className="text-gray-300 mb-8">{error}</p>
          <Link href="/" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (showAd) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono">
              Redirecting in {countdown}s
            </div>
          </div>
        </nav>

        <div className="flex items-center justify-center min-h-[calc(100vh-80px)] p-4">
          <div className="max-w-2xl w-full">
            {/* Ad Section */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
              <h2 className="text-2xl font-bold text-white mb-4 text-center">
                {!countdownStarted ? (
                  <>🎮 Click the Ad to Continue to Your Link!</>
                ) : (
                  <>⏰ Redirecting in {countdown} seconds...</>
                )}
              </h2>

              {!countdownStarted && (
                <div className="text-center mb-4">
                  <p className="text-yellow-300 text-sm">
                    ⚠️ You must click the advertisement below to start the countdown
                  </p>
                  <p className="text-gray-400 text-xs mt-1">
                    Or wait {fallbackTimer} seconds for automatic redirect
                  </p>
                </div>
              )}

              {/* Banner Ad Code Container */}
              <div className="bg-white/5 rounded-lg p-6 mb-6 text-center">
                <p className="text-gray-300 text-sm mb-4">Advertisement - Click any banner to continue</p>
                <div
                  id="banner-ad-container"
                  className="min-h-[200px] flex items-center justify-center cursor-pointer"
                  onClick={handleAdClick}
                >
                  {bannerAdsLoaded ? (
                    <div className="w-full">
                      {/* Banner ads will be injected here by the script */}
                      <div className="text-gray-400 text-sm">
                        {adClicked ? 'Ad clicked! Countdown started' : 'Click the banner ad above to start countdown'}
                      </div>
                    </div>
                  ) : (
                    <div className="text-gray-400 text-sm">Loading banner ads...</div>
                  )}
                </div>
              </div>

              {/* Fallback Gaming Ad - Always clickable */}
              <div
                onClick={handleAdClick}
                className={`bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 cursor-pointer hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 mb-6 ${
                  adClicked ? 'opacity-50 pointer-events-none' : ''
                }`}
              >
                <div className="text-center text-white">
                  <h3 className="text-lg font-bold mb-2">🚀 Epic Gaming Deals!</h3>
                  <p className="mb-3">Get up to 70% off on the latest games and gaming gear!</p>
                  <div className="bg-white/20 rounded-lg p-3">
                    <p className="text-sm font-semibold">
                      {adClicked ? 'Ad Clicked! Countdown Started' : 'Click Here to Continue'}
                    </p>
                    <p className="text-xs opacity-90">
                      {adClicked ? 'Redirecting soon...' : 'Click to start 30-second countdown'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Countdown Display */}
              <div className="text-center">
                {countdownStarted ? (
                  <>
                    <div className="bg-green-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold animate-pulse">
                      {countdown}
                    </div>
                    <p className="text-green-300 mb-4">
                      ✅ Ad clicked! Redirecting in {countdown} seconds...
                    </p>
                  </>
                ) : (
                  <>
                    <div className="bg-yellow-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                      {fallbackTimer}
                    </div>
                    <p className="text-yellow-300 mb-4">
                      ⏳ Click an ad above or wait {fallbackTimer} seconds for automatic redirect
                    </p>
                  </>
                )}

                <div className="bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold">
                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}
                </div>
              </div>
            </div>

            {/* Link Preview */}
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
              <p className="text-gray-400 text-sm mb-2">You will be redirected to:</p>
              <p className="text-purple-400 font-mono break-all">{linkData?.originalUrl}</p>
            </div>
          </div>
        </div>

        {/* Montag Ad Network Script */}
        <Script
          src="https://fpyf8.com/88/tag.min.js"
          data-zone="156349"
          async
          data-cfasync="false"
          strategy="afterInteractive"
        />
      </div>
    );
  }

  // Show final redirect page
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">Ready to Redirect!</h2>
          <p className="text-gray-300 mb-6">Click the button below to visit your destination:</p>
          <p className="text-purple-400 font-mono text-sm mb-6 break-all">{linkData?.originalUrl}</p>
          
          <button
            onClick={handleRedirect}
            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full"
          >
            Visit Link →
          </button>
          
          <Link href="/" className="block mt-4 text-purple-400 hover:text-purple-300 transition-colors">
            ← Back to GameHub
          </Link>
        </div>
      </div>

      {/* Montag Ad Network Script */}
      <Script
        src="https://fpyf8.com/88/tag.min.js"
        data-zone="156349"
        async
        data-cfasync="false"
        strategy="afterInteractive"
      />
    </div>
  );
}
