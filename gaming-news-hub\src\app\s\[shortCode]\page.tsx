'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function RedirectPage() {
  const params = useParams();
  const shortCode = params.shortCode as string;
  
  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [countdown, setCountdown] = useState(30); // 30 seconds countdown
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adClicked, setAdClicked] = useState(false);
  const [countdownStarted, setCountdownStarted] = useState(false);
  const [bannerAdsLoaded, setBannerAdsLoaded] = useState(false);

  // Fetch link data
  useEffect(() => {
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        const data = await response.json();
        
        if (response.ok) {
          setLinkData(data);
        } else {
          setError(data.error || 'Link not found');
        }
      } catch (err) {
        setError('Failed to load link data');
      } finally {
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Countdown timer
  useEffect(() => {
    if (!countdownStarted || countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdownStarted, countdown]);

  // Load banner ads after component mounts
  useEffect(() => {
    if (!loading && !error) {
      // Set a small delay to ensure the page is fully loaded
      const timer = setTimeout(() => {
        setBannerAdsLoaded(true);
        console.log('Banner ads container loaded');
      }, 2000); // Increased delay to 2 seconds
      return () => clearTimeout(timer);
    }
  }, [loading, error]);

  // Global click listener for ad overlays (Vignette Banner & In-Page Push)
  useEffect(() => {
    if (bannerAdsLoaded && !adClicked) {
      const handleGlobalClick = (event: MouseEvent) => {
        const target = event.target as HTMLElement;

        // Check if click is on an ad element (common ad class names and attributes)
        const isAdClick = target.closest('[class*="ad"]') ||
                         target.closest('[id*="ad"]') ||
                         target.closest('[class*="banner"]') ||
                         target.closest('[class*="popup"]') ||
                         target.closest('[class*="overlay"]') ||
                         target.hasAttribute('data-ad') ||
                         target.tagName === 'IFRAME';

        if (isAdClick) {
          console.log('Ad overlay clicked!', target);
          handleAdClick();
        }
      };

      // Add global click listener
      document.addEventListener('click', handleGlobalClick, true);

      // Also check for ad elements periodically
      const checkAds = setInterval(() => {
        const adElements = document.querySelectorAll('[class*="ad"], [id*="ad"], iframe');
        if (adElements.length > 0) {
          console.log('Ad elements detected:', adElements.length);
        }
      }, 3000);

      return () => {
        document.removeEventListener('click', handleGlobalClick, true);
        clearInterval(checkAds);
      };
    }
  }, [bannerAdsLoaded, adClicked]);

  const handleAdClick = () => {
    console.log('Ad clicked! Starting 30-second countdown...');
    setAdClicked(true);
    setCountdownStarted(true);
    // Reset countdown to 30 seconds when ad is clicked
    setCountdown(30);
  };

  const handleRedirect = () => {
    if (linkData?.originalUrl) {
      window.location.href = linkData.originalUrl;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white text-xl">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="bg-red-500/20 border border-red-500 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-red-400 mb-4">Error</h2>
            <p className="text-gray-300 mb-6">{error}</p>
            <Link href="/" className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
              Go Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
            🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
          </Link>
          <div className="text-purple-400 font-mono">
            {countdownStarted ? `Redirecting in ${countdown}s` : 'Click ad to start'}
          </div>
        </div>
      </nav>

      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] p-4">
        <div className="max-w-2xl w-full">
          {/* Ad Section */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
            <h2 className="text-2xl font-bold text-white mb-4 text-center">
              Please wait while we prepare your link
            </h2>

            {!countdownStarted && (
              <div className="text-center mb-4">
                <p className="text-yellow-300 text-sm">
                  ⚠️ You must click the advertisement below to start the countdown
                </p>
              </div>
            )}

            {/* Banner Ad Code Container */}
            <div className="bg-white/5 rounded-lg p-6 mb-6 text-center">
              <p className="text-gray-300 text-sm mb-4">Advertisement - Click any banner to continue</p>
              <div
                id="banner-ad-container"
                className="min-h-[200px] flex items-center justify-center cursor-pointer"
                onClick={handleAdClick}
              >
                {bannerAdsLoaded ? (
                  <div className="w-full">
                    {/* Vignette Banner & In-Page Push ads will appear as overlays */}
                    <div
                      className="w-full min-h-[200px] bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-lg border border-purple-500/30 flex flex-col items-center justify-center p-8 cursor-pointer hover:from-purple-900/30 hover:to-pink-900/30 transition-all"
                      onClick={handleAdClick}
                    >
                      <div className="text-center">
                        <div className="text-4xl mb-4">🎯</div>
                        <h3 className="text-white text-lg font-semibold mb-2">Advertisement Zone</h3>
                        <p className="text-gray-300 text-sm mb-4">
                          Vignette Banner & In-Page Push ads will appear as overlays
                        </p>
                        <p className="text-purple-300 text-xs mb-4">
                          Click here if ads don't appear automatically
                        </p>
                        <div className="flex items-center justify-center space-x-2">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>

                    <div className="text-gray-400 text-sm text-center mt-4">
                      {adClicked ? '✅ Ad clicked! Countdown started' : '👆 Click on any ad popup/overlay to start countdown'}
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-400 text-sm">Loading advertisement scripts...</div>
                )}
              </div>
            </div>

            {/* Countdown Display */}
            <div className="text-center mb-6">
              <div className="bg-white/5 rounded-lg p-6">
                {countdownStarted ? (
                  <div className="text-center">
                    <div className="bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                      {countdown}
                    </div>
                    <p className="text-purple-300 mb-4">
                      ⏳ Please wait {countdown} seconds...
                    </p>
                  </div>
                ) : (
                  <div className="text-center">
                    <p className="text-yellow-300 mb-4">
                      ⏳ Click an advertisement above to start the countdown
                    </p>
                  </div>
                )}

                <div className="bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold">
                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}
                </div>
              </div>
            </div>



            {/* Final redirect section - only show after countdown */}
            {countdown <= 0 && countdownStarted && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mt-6">
                <h2 className="text-2xl font-bold text-white mb-4 text-center">Link Ready!</h2>
                <p className="text-gray-300 mb-6 text-center">Click the button below to get your link:</p>

                <button
                  onClick={handleRedirect}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full"
                >
                  Get Your Link
                </button>

                <Link href="/" className="block mt-4 text-purple-400 hover:text-purple-300 transition-colors text-center">
                  ← Back to GameHub
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Banner Ad Script 1 */}
      <Script
        dangerouslySetInnerHTML={{
          __html: `
            console.log('Loading banner ad script 1...');
            (function(d,z,s){
              s.src='https://'+d+'/401/'+z;
              s.onload = function() { console.log('Banner ad script 1 loaded'); };
              s.onerror = function() { console.log('Banner ad script 1 failed to load'); };
              try{
                (document.body||document.documentElement).appendChild(s);
              }catch(e){
                console.log('Error appending banner ad script 1:', e);
              }
            })('gizokraijaw.net',9550341,document.createElement('script'))
          `
        }}
        strategy="beforeInteractive"
      />

      {/* Banner Ad Script 2 */}
      <Script
        dangerouslySetInnerHTML={{
          __html: `
            console.log('Loading banner ad script 2...');
            (function(d,z,s){
              s.src='https://'+d+'/400/'+z;
              s.onload = function() { console.log('Banner ad script 2 loaded'); };
              s.onerror = function() { console.log('Banner ad script 2 failed to load'); };
              try{
                (document.body||document.documentElement).appendChild(s);
              }catch(e){
                console.log('Error appending banner ad script 2:', e);
              }
            })('vemtoutcheeg.com',9550670,document.createElement('script'))
          `
        }}
        strategy="beforeInteractive"
      />

      {/* Montag Ad Network Script */}
      <Script
        src="https://fpyf8.com/88/tag.min.js"
        data-zone="156349"
        async
        data-cfasync="false"
        strategy="afterInteractive"
      />
    </div>
  );
}
