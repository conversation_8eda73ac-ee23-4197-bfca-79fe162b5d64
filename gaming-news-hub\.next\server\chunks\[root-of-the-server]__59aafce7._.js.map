{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/thumbnailExtractor.ts"], "sourcesContent": ["// Thumbnail extraction utility for product URLs\n\nexport interface ProductThumbnail {\n  url: string;\n  alt: string;\n  fallback: string;\n}\n\n// Extract domain from URL for fallback logic\nfunction getDomain(url: string): string {\n  try {\n    return new URL(url).hostname.replace('www.', '');\n  } catch {\n    return 'unknown';\n  }\n}\n\n// Generate fallback thumbnail based on product title and domain\nfunction generateFallbackThumbnail(title: string): ProductThumbnail {\n  const colors = [\n    'from-blue-500 to-purple-600',\n    'from-green-500 to-blue-600', \n    'from-red-500 to-pink-600',\n    'from-yellow-500 to-orange-600',\n    'from-purple-500 to-pink-600',\n    'from-cyan-500 to-blue-600',\n    'from-orange-500 to-red-600',\n    'from-teal-500 to-green-600'\n  ];\n  \n  // Use title hash to consistently pick a color\n  const colorIndex = title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;\n  \n  return {\n    url: '', // No actual image URL\n    alt: title,\n    fallback: colors[colorIndex]\n  };\n}\n\n// Extract thumbnail from different e-commerce platforms\nexport async function extractThumbnail(productUrl: string, title: string): Promise<ProductThumbnail> {\n  const domain = getDomain(productUrl);\n  \n  try {\n    // For Amazon products, try to extract ASIN and use Amazon's image API\n    if (domain.includes('amazon')) {\n      const asinMatch = productUrl.match(/\\/dp\\/([A-Z0-9]{10})/);\n      if (asinMatch) {\n        const asin = asinMatch[1];\n        return {\n          url: `https://images-na.ssl-images-amazon.com/images/P/${asin}.01.L.jpg`,\n          alt: title,\n          fallback: generateFallbackThumbnail(title).fallback\n        };\n      }\n    }\n    \n    // For eBay products\n    if (domain.includes('ebay')) {\n      // eBay thumbnails are harder to extract without API, use fallback\n      return generateFallbackThumbnail(title);\n    }\n    \n    // For Steam products\n    if (domain.includes('steampowered') || domain.includes('store.steampowered')) {\n      const appIdMatch = productUrl.match(/\\/app\\/(\\d+)/);\n      if (appIdMatch) {\n        const appId = appIdMatch[1];\n        return {\n          url: `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`,\n          alt: title,\n          fallback: generateFallbackThumbnail(title).fallback\n        };\n      }\n    }\n    \n    // For Best Buy products\n    if (domain.includes('bestbuy')) {\n      // Best Buy uses product SKU in URL\n      const skuMatch = productUrl.match(/\\/(\\d{7})/);\n      if (skuMatch) {\n        // Best Buy doesn't have a direct image API, use fallback\n        return generateFallbackThumbnail(title);\n      }\n    }\n    \n    // For Newegg products\n    if (domain.includes('newegg')) {\n      return generateFallbackThumbnail(title);\n    }\n\n    // Default fallback for unknown domains\n    return generateFallbackThumbnail(title);\n    \n  } catch (error) {\n    console.error('Error extracting thumbnail:', error);\n    return generateFallbackThumbnail(title);\n  }\n}\n\n// Get product emoji based on title keywords\nexport function getProductEmoji(title: string): string {\n  const titleLower = title.toLowerCase();\n  \n  if (titleLower.includes('controller') || titleLower.includes('gamepad')) return '🎮';\n  if (titleLower.includes('headset') || titleLower.includes('headphone')) return '🎧';\n  if (titleLower.includes('keyboard')) return '⌨️';\n  if (titleLower.includes('mouse')) return '🖱️';\n  if (titleLower.includes('monitor') || titleLower.includes('display')) return '🖥️';\n  if (titleLower.includes('chair')) return '🪑';\n  if (titleLower.includes('microphone') || titleLower.includes('mic')) return '🎤';\n  if (titleLower.includes('webcam') || titleLower.includes('camera')) return '📷';\n  if (titleLower.includes('laptop') || titleLower.includes('computer')) return '💻';\n  if (titleLower.includes('tablet')) return '📱';\n  if (titleLower.includes('speaker')) return '🔊';\n  if (titleLower.includes('cable') || titleLower.includes('cord')) return '🔌';\n  if (titleLower.includes('stand') || titleLower.includes('mount')) return '🗂️';\n  if (titleLower.includes('light') || titleLower.includes('led') || titleLower.includes('rgb')) return '💡';\n  if (titleLower.includes('case') || titleLower.includes('cover')) return '📦';\n  \n  return '🎮'; // Default gaming emoji\n}\n\n// Calculate discount percentage\nexport function calculateDiscount(originalPrice: number, salePrice: number): number {\n  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;AAQhD,6CAA6C;AAC7C,SAAS,UAAU,GAAW;IAC5B,IAAI;QACF,OAAO,IAAI,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,QAAQ;IAC/C,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,gEAAgE;AAChE,SAAS,0BAA0B,KAAa;IAC9C,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,8CAA8C;IAC9C,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,IAAI,KAAK,OAAO,MAAM;IAErG,OAAO;QACL,KAAK;QACL,KAAK;QACL,UAAU,MAAM,CAAC,WAAW;IAC9B;AACF;AAGO,eAAe,iBAAiB,UAAkB,EAAE,KAAa;IACtE,MAAM,SAAS,UAAU;IAEzB,IAAI;QACF,sEAAsE;QACtE,IAAI,OAAO,QAAQ,CAAC,WAAW;YAC7B,MAAM,YAAY,WAAW,KAAK,CAAC;YACnC,IAAI,WAAW;gBACb,MAAM,OAAO,SAAS,CAAC,EAAE;gBACzB,OAAO;oBACL,KAAK,CAAC,iDAAiD,EAAE,KAAK,SAAS,CAAC;oBACxE,KAAK;oBACL,UAAU,0BAA0B,OAAO,QAAQ;gBACrD;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,OAAO,QAAQ,CAAC,SAAS;YAC3B,kEAAkE;YAClE,OAAO,0BAA0B;QACnC;QAEA,qBAAqB;QACrB,IAAI,OAAO,QAAQ,CAAC,mBAAmB,OAAO,QAAQ,CAAC,uBAAuB;YAC5E,MAAM,aAAa,WAAW,KAAK,CAAC;YACpC,IAAI,YAAY;gBACd,MAAM,QAAQ,UAAU,CAAC,EAAE;gBAC3B,OAAO;oBACL,KAAK,CAAC,8CAA8C,EAAE,MAAM,WAAW,CAAC;oBACxE,KAAK;oBACL,UAAU,0BAA0B,OAAO,QAAQ;gBACrD;YACF;QACF;QAEA,wBAAwB;QACxB,IAAI,OAAO,QAAQ,CAAC,YAAY;YAC9B,mCAAmC;YACnC,MAAM,WAAW,WAAW,KAAK,CAAC;YAClC,IAAI,UAAU;gBACZ,yDAAyD;gBACzD,OAAO,0BAA0B;YACnC;QACF;QAEA,sBAAsB;QACtB,IAAI,OAAO,QAAQ,CAAC,WAAW;YAC7B,OAAO,0BAA0B;QACnC;QAEA,uCAAuC;QACvC,OAAO,0BAA0B;IAEnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,0BAA0B;IACnC;AACF;AAGO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,aAAa,MAAM,WAAW;IAEpC,IAAI,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,YAAY,OAAO;IAChF,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,cAAc,OAAO;IAC/E,IAAI,WAAW,QAAQ,CAAC,aAAa,OAAO;IAC5C,IAAI,WAAW,QAAQ,CAAC,UAAU,OAAO;IACzC,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC7E,IAAI,WAAW,QAAQ,CAAC,UAAU,OAAO;IACzC,IAAI,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,QAAQ,OAAO;IAC5E,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,WAAW,OAAO;IAC3E,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,aAAa,OAAO;IAC7E,IAAI,WAAW,QAAQ,CAAC,WAAW,OAAO;IAC1C,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,SAAS,OAAO;IACxE,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,UAAU,OAAO;IACzE,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,QAAQ,OAAO;IACrG,IAAI,WAAW,QAAQ,CAAC,WAAW,WAAW,QAAQ,CAAC,UAAU,OAAO;IAExE,OAAO,MAAM,uBAAuB;AACtC;AAGO,SAAS,kBAAkB,aAAqB,EAAE,SAAiB;IACxE,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,SAAS,IAAI,gBAAiB;AACpE", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/dealsParser.ts"], "sourcesContent": ["import { extractThumbnail, getProductEmoji, calculateDiscount, ProductThumbnail } from './thumbnailExtractor';\n\nexport interface Deal {\n  id: string;\n  title: string;\n  originalPrice: number;\n  salePrice: number;\n  productUrl: string;\n  thumbnail: ProductThumbnail;\n  emoji: string;\n  discountPercentage: number;\n}\n\n// Parse deal string format: TITLE|ORIGINAL_PRICE|SALE_PRICE|PRODUCT_URL\nfunction parseDealString(dealString: string, dealId: string): Omit<Deal, 'thumbnail'> | null {\n  try {\n    const parts = dealString.split('|');\n    if (parts.length !== 4) {\n      console.warn(`Invalid deal format for ${dealId}: ${dealString}`);\n      return null;\n    }\n\n    const [title, originalPriceStr, salePriceStr, productUrl] = parts;\n    const originalPrice = parseFloat(originalPriceStr);\n    const salePrice = parseFloat(salePriceStr);\n\n    if (isNaN(originalPrice) || isNaN(salePrice)) {\n      console.warn(`Invalid prices for ${dealId}: ${originalPriceStr}, ${salePriceStr}`);\n      return null;\n    }\n\n    if (salePrice >= originalPrice) {\n      console.warn(`Sale price should be less than original price for ${dealId}`);\n      return null;\n    }\n\n    return {\n      id: dealId,\n      title: title.trim(),\n      originalPrice,\n      salePrice,\n      productUrl: productUrl.trim(),\n      emoji: getProductEmoji(title),\n      discountPercentage: calculateDiscount(originalPrice, salePrice)\n    };\n  } catch (error) {\n    console.error(`Error parsing deal ${dealId}:`, error);\n    return null;\n  }\n}\n\n// Get all deals from environment variables\nexport async function getDealsFromEnv(): Promise<Deal[]> {\n  const deals: Deal[] = [];\n  \n  // Look for environment variables starting with DEAL_\n  const envVars = process.env;\n  const dealKeys = Object.keys(envVars)\n    .filter(key => key.startsWith('DEAL_'))\n    .sort((a, b) => {\n      // Sort by number: DEAL_1, DEAL_2, etc.\n      const numA = parseInt(a.replace('DEAL_', ''));\n      const numB = parseInt(b.replace('DEAL_', ''));\n      return numA - numB;\n    });\n\n  for (const key of dealKeys) {\n    const dealString = envVars[key];\n    if (!dealString) continue;\n\n    const parsedDeal = parseDealString(dealString, key);\n    if (!parsedDeal) continue;\n\n    try {\n      // Extract thumbnail for the product\n      const thumbnail = await extractThumbnail(parsedDeal.productUrl, parsedDeal.title);\n      \n      deals.push({\n        ...parsedDeal,\n        thumbnail\n      });\n    } catch (error) {\n      console.error(`Error extracting thumbnail for ${key}:`, error);\n      // Still add the deal with fallback thumbnail\n      deals.push({\n        ...parsedDeal,\n        thumbnail: {\n          url: '',\n          alt: parsedDeal.title,\n          fallback: 'from-gray-500 to-gray-600'\n        }\n      });\n    }\n  }\n\n  return deals;\n}\n\n// Get deals for client-side rendering (cached version)\nexport function getStaticDeals(): Omit<Deal, 'thumbnail'>[] {\n  const deals: Omit<Deal, 'thumbnail'>[] = [];\n  \n  // This runs at build time, so we can access process.env\n  const envVars = process.env;\n  const dealKeys = Object.keys(envVars)\n    .filter(key => key.startsWith('DEAL_'))\n    .sort((a, b) => {\n      const numA = parseInt(a.replace('DEAL_', ''));\n      const numB = parseInt(b.replace('DEAL_', ''));\n      return numA - numB;\n    });\n\n  for (const key of dealKeys) {\n    const dealString = envVars[key];\n    if (!dealString) continue;\n\n    const parsedDeal = parseDealString(dealString, key);\n    if (parsedDeal) {\n      deals.push(parsedDeal);\n    }\n  }\n\n  return deals;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,wEAAwE;AACxE,SAAS,gBAAgB,UAAkB,EAAE,MAAc;IACzD,IAAI;QACF,MAAM,QAAQ,WAAW,KAAK,CAAC;QAC/B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO,EAAE,EAAE,YAAY;YAC/D,OAAO;QACT;QAEA,MAAM,CAAC,OAAO,kBAAkB,cAAc,WAAW,GAAG;QAC5D,MAAM,gBAAgB,WAAW;QACjC,MAAM,YAAY,WAAW;QAE7B,IAAI,MAAM,kBAAkB,MAAM,YAAY;YAC5C,QAAQ,IAAI,CAAC,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,iBAAiB,EAAE,EAAE,cAAc;YACjF,OAAO;QACT;QAEA,IAAI,aAAa,eAAe;YAC9B,QAAQ,IAAI,CAAC,CAAC,kDAAkD,EAAE,QAAQ;YAC1E,OAAO;QACT;QAEA,OAAO;YACL,IAAI;YACJ,OAAO,MAAM,IAAI;YACjB;YACA;YACA,YAAY,WAAW,IAAI;YAC3B,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE;YACvB,oBAAoB,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;QACvD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC,EAAE;QAC/C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,QAAgB,EAAE;IAExB,qDAAqD;IACrD,MAAM,UAAU,QAAQ,GAAG;IAC3B,MAAM,WAAW,OAAO,IAAI,CAAC,SAC1B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,UAC7B,IAAI,CAAC,CAAC,GAAG;QACR,uCAAuC;QACvC,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,OAAO,OAAO;IAChB;IAEF,KAAK,MAAM,OAAO,SAAU;QAC1B,MAAM,aAAa,OAAO,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY;QAEjB,MAAM,aAAa,gBAAgB,YAAY;QAC/C,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,oCAAoC;YACpC,MAAM,YAAY,MAAM,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,UAAU,EAAE,WAAW,KAAK;YAEhF,MAAM,IAAI,CAAC;gBACT,GAAG,UAAU;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC,EAAE;YACxD,6CAA6C;YAC7C,MAAM,IAAI,CAAC;gBACT,GAAG,UAAU;gBACb,WAAW;oBACT,KAAK;oBACL,KAAK,WAAW,KAAK;oBACrB,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAmC,EAAE;IAE3C,wDAAwD;IACxD,MAAM,UAAU,QAAQ,GAAG;IAC3B,MAAM,WAAW,OAAO,IAAI,CAAC,SAC1B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,UAC7B,IAAI,CAAC,CAAC,GAAG;QACR,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,MAAM,OAAO,SAAS,EAAE,OAAO,CAAC,SAAS;QACzC,OAAO,OAAO;IAChB;IAEF,KAAK,MAAM,OAAO,SAAU;QAC1B,MAAM,aAAa,OAAO,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY;QAEjB,MAAM,aAAa,gBAAgB,YAAY;QAC/C,IAAI,YAAY;YACd,MAAM,IAAI,CAAC;QACb;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/api/deals/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getDealsFromEnv, Deal } from '@/lib/dealsParser';\n\n// Cache deals for 5 minutes to avoid repeated thumbnail extraction\nlet cachedDeals: Deal[] | null = null;\nlet cacheTime = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nexport async function GET() {\n  try {\n    const now = Date.now();\n    \n    // Return cached deals if still valid\n    if (cachedDeals && (now - cacheTime) < CACHE_DURATION) {\n      return NextResponse.json({ deals: cachedDeals });\n    }\n    \n    // Fetch fresh deals\n    const deals = await getDealsFromEnv();\n    \n    // Update cache\n    cachedDeals = deals;\n    cacheTime = now;\n    \n    return NextResponse.json({ deals });\n  } catch (error) {\n    console.error('Error fetching deals:', error);\n    return NextResponse.json({ error: 'Failed to fetch deals' }, { status: 500 });\n  }\n}\n\n// Force refresh cache (useful for development)\nexport async function POST() {\n  try {\n    cachedDeals = null;\n    cacheTime = 0;\n    \n    const deals = await getDealsFromEnv();\n    cachedDeals = deals;\n    cacheTime = Date.now();\n    \n    return NextResponse.json({ deals, message: 'Cache refreshed' });\n  } catch (error) {\n    console.error('Error refreshing deals cache:', error);\n    return NextResponse.json({ error: 'Failed to refresh deals' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,mEAAmE;AACnE,IAAI,cAA6B;AACjC,IAAI,YAAY;AAChB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAE3C,eAAe;IACpB,IAAI;QACF,MAAM,MAAM,KAAK,GAAG;QAEpB,qCAAqC;QACrC,IAAI,eAAe,AAAC,MAAM,YAAa,gBAAgB;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAY;QAChD;QAEA,oBAAoB;QACpB,MAAM,QAAQ,MAAM,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD;QAElC,eAAe;QACf,cAAc;QACd,YAAY;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe;IACpB,IAAI;QACF,cAAc;QACd,YAAY;QAEZ,MAAM,QAAQ,MAAM,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD;QAClC,cAAc;QACd,YAAY,KAAK,GAAG;QAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;YAAO,SAAS;QAAkB;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;AACF", "debugId": null}}]}