module.exports = {

"[project]/.next-internal/server/app/api/deals/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/thumbnailExtractor.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Thumbnail extraction utility for product URLs
__turbopack_context__.s({
    "calculateDiscount": (()=>calculateDiscount),
    "extractThumbnail": (()=>extractThumbnail),
    "getProductEmoji": (()=>getProductEmoji)
});
// Extract domain from URL for fallback logic
function getDomain(url) {
    try {
        return new URL(url).hostname.replace('www.', '');
    } catch  {
        return 'unknown';
    }
}
// Generate fallback thumbnail based on product title and domain
function generateFallbackThumbnail(title) {
    const colors = [
        'from-blue-500 to-purple-600',
        'from-green-500 to-blue-600',
        'from-red-500 to-pink-600',
        'from-yellow-500 to-orange-600',
        'from-purple-500 to-pink-600',
        'from-cyan-500 to-blue-600',
        'from-orange-500 to-red-600',
        'from-teal-500 to-green-600'
    ];
    // Use title hash to consistently pick a color
    const colorIndex = title.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0) % colors.length;
    return {
        url: '',
        alt: title,
        fallback: colors[colorIndex]
    };
}
async function extractThumbnail(productUrl, title) {
    const domain = getDomain(productUrl);
    try {
        // For Amazon products, try to extract ASIN and use Amazon's image API
        if (domain.includes('amazon')) {
            const asinMatch = productUrl.match(/\/dp\/([A-Z0-9]{10})/);
            if (asinMatch) {
                const asin = asinMatch[1];
                return {
                    url: `https://images-na.ssl-images-amazon.com/images/P/${asin}.01.L.jpg`,
                    alt: title,
                    fallback: generateFallbackThumbnail(title).fallback
                };
            }
        }
        // For eBay products
        if (domain.includes('ebay')) {
            // eBay thumbnails are harder to extract without API, use fallback
            return generateFallbackThumbnail(title);
        }
        // For Steam products
        if (domain.includes('steampowered') || domain.includes('store.steampowered')) {
            const appIdMatch = productUrl.match(/\/app\/(\d+)/);
            if (appIdMatch) {
                const appId = appIdMatch[1];
                return {
                    url: `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`,
                    alt: title,
                    fallback: generateFallbackThumbnail(title).fallback
                };
            }
        }
        // For Best Buy products
        if (domain.includes('bestbuy')) {
            // Best Buy uses product SKU in URL
            const skuMatch = productUrl.match(/\/(\d{7})/);
            if (skuMatch) {
                // Best Buy doesn't have a direct image API, use fallback
                return generateFallbackThumbnail(title);
            }
        }
        // For Newegg products
        if (domain.includes('newegg')) {
            return generateFallbackThumbnail(title);
        }
        // Default fallback for unknown domains
        return generateFallbackThumbnail(title);
    } catch (error) {
        console.error('Error extracting thumbnail:', error);
        return generateFallbackThumbnail(title);
    }
}
function getProductEmoji(title) {
    const titleLower = title.toLowerCase();
    if (titleLower.includes('controller') || titleLower.includes('gamepad')) return '🎮';
    if (titleLower.includes('headset') || titleLower.includes('headphone')) return '🎧';
    if (titleLower.includes('keyboard')) return '⌨️';
    if (titleLower.includes('mouse')) return '🖱️';
    if (titleLower.includes('monitor') || titleLower.includes('display')) return '🖥️';
    if (titleLower.includes('chair')) return '🪑';
    if (titleLower.includes('microphone') || titleLower.includes('mic')) return '🎤';
    if (titleLower.includes('webcam') || titleLower.includes('camera')) return '📷';
    if (titleLower.includes('laptop') || titleLower.includes('computer')) return '💻';
    if (titleLower.includes('tablet')) return '📱';
    if (titleLower.includes('speaker')) return '🔊';
    if (titleLower.includes('cable') || titleLower.includes('cord')) return '🔌';
    if (titleLower.includes('stand') || titleLower.includes('mount')) return '🗂️';
    if (titleLower.includes('light') || titleLower.includes('led') || titleLower.includes('rgb')) return '💡';
    if (titleLower.includes('case') || titleLower.includes('cover')) return '📦';
    return '🎮'; // Default gaming emoji
}
function calculateDiscount(originalPrice, salePrice) {
    return Math.round((originalPrice - salePrice) / originalPrice * 100);
}
}}),
"[project]/src/lib/dealsParser.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDealsFromEnv": (()=>getDealsFromEnv),
    "getStaticDeals": (()=>getStaticDeals)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$thumbnailExtractor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/thumbnailExtractor.ts [app-route] (ecmascript)");
;
// Parse deal string format: TITLE|ORIGINAL_PRICE|SALE_PRICE|PRODUCT_URL
function parseDealString(dealString, dealId) {
    try {
        const parts = dealString.split('|');
        if (parts.length !== 4) {
            console.warn(`Invalid deal format for ${dealId}: ${dealString}`);
            return null;
        }
        const [title, originalPriceStr, salePriceStr, productUrl] = parts;
        const originalPrice = parseFloat(originalPriceStr);
        const salePrice = parseFloat(salePriceStr);
        if (isNaN(originalPrice) || isNaN(salePrice)) {
            console.warn(`Invalid prices for ${dealId}: ${originalPriceStr}, ${salePriceStr}`);
            return null;
        }
        if (salePrice >= originalPrice) {
            console.warn(`Sale price should be less than original price for ${dealId}`);
            return null;
        }
        return {
            id: dealId,
            title: title.trim(),
            originalPrice,
            salePrice,
            productUrl: productUrl.trim(),
            emoji: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$thumbnailExtractor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getProductEmoji"])(title),
            discountPercentage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$thumbnailExtractor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateDiscount"])(originalPrice, salePrice)
        };
    } catch (error) {
        console.error(`Error parsing deal ${dealId}:`, error);
        return null;
    }
}
async function getDealsFromEnv() {
    const deals = [];
    // Look for environment variables starting with DEAL_
    const envVars = process.env;
    const dealKeys = Object.keys(envVars).filter((key)=>key.startsWith('DEAL_')).sort((a, b)=>{
        // Sort by number: DEAL_1, DEAL_2, etc.
        const numA = parseInt(a.replace('DEAL_', ''));
        const numB = parseInt(b.replace('DEAL_', ''));
        return numA - numB;
    });
    for (const key of dealKeys){
        const dealString = envVars[key];
        if (!dealString) continue;
        const parsedDeal = parseDealString(dealString, key);
        if (!parsedDeal) continue;
        try {
            // Extract thumbnail for the product
            const thumbnail = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$thumbnailExtractor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractThumbnail"])(parsedDeal.productUrl, parsedDeal.title);
            deals.push({
                ...parsedDeal,
                thumbnail
            });
        } catch (error) {
            console.error(`Error extracting thumbnail for ${key}:`, error);
            // Still add the deal with fallback thumbnail
            deals.push({
                ...parsedDeal,
                thumbnail: {
                    url: '',
                    alt: parsedDeal.title,
                    fallback: 'from-gray-500 to-gray-600'
                }
            });
        }
    }
    return deals;
}
function getStaticDeals() {
    const deals = [];
    // This runs at build time, so we can access process.env
    const envVars = process.env;
    const dealKeys = Object.keys(envVars).filter((key)=>key.startsWith('DEAL_')).sort((a, b)=>{
        const numA = parseInt(a.replace('DEAL_', ''));
        const numB = parseInt(b.replace('DEAL_', ''));
        return numA - numB;
    });
    for (const key of dealKeys){
        const dealString = envVars[key];
        if (!dealString) continue;
        const parsedDeal = parseDealString(dealString, key);
        if (parsedDeal) {
            deals.push(parsedDeal);
        }
    }
    return deals;
}
}}),
"[project]/src/app/api/deals/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dealsParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dealsParser.ts [app-route] (ecmascript)");
;
;
// Cache deals for 5 minutes to avoid repeated thumbnail extraction
let cachedDeals = null;
let cacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
async function GET() {
    try {
        const now = Date.now();
        // Return cached deals if still valid
        if (cachedDeals && now - cacheTime < CACHE_DURATION) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                deals: cachedDeals
            });
        }
        // Fetch fresh deals
        const deals = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dealsParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDealsFromEnv"])();
        // Update cache
        cachedDeals = deals;
        cacheTime = now;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            deals
        });
    } catch (error) {
        console.error('Error fetching deals:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch deals'
        }, {
            status: 500
        });
    }
}
async function POST() {
    try {
        cachedDeals = null;
        cacheTime = 0;
        const deals = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dealsParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDealsFromEnv"])();
        cachedDeals = deals;
        cacheTime = Date.now();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            deals,
            message: 'Cache refreshed'
        });
    } catch (error) {
        console.error('Error refreshing deals cache:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to refresh deals'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__59aafce7._.js.map