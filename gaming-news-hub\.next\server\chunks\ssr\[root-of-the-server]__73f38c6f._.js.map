{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/page1/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\n\ninterface LinkData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function Page1() {\n  const params = useParams();\n  const router = useRouter();\n  const shortCode = params.shortCode as string;\n\n  const [linkData, setLinkData] = useState<LinkData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showContinue, setShowContinue] = useState(false);\n\n  // Fetch link data\n  useEffect(() => {\n    const fetchLinkData = async () => {\n      try {\n        const response = await fetch(`/api/redirect/${shortCode}`);\n        const data = await response.json();\n\n        if (response.ok) {\n          setLinkData(data);\n        } else {\n          setError(data.error || 'Link not found');\n        }\n      } catch (err) {\n        setError('Failed to load link data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLinkData();\n  }, [shortCode]);\n\n  // Show continue button after 20 seconds\n  useEffect(() => {\n    if (!loading && !error) {\n      const timer = setTimeout(() => {\n        setShowContinue(true);\n      }, 20000); // 20 seconds\n\n      return () => clearTimeout(timer);\n    }\n  }, [loading, error]);\n\n  const handleContinue = () => {\n    router.push(`/s/${shortCode}/page2`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-white text-xl\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center max-w-md\">\n          <div className=\"bg-red-500/20 border border-red-500 rounded-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-red-400 mb-4\">Error</h2>\n            <p className=\"text-gray-300 mb-6\">{error}</p>\n            <Link href=\"/\" className=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\">\n              Go Home\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n        <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n          <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n            🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n          </Link>\n          <div className=\"text-purple-400 font-mono\">\n            Preparing your link...\n          </div>\n        </div>\n      </nav>\n\n      <div className=\"max-w-4xl mx-auto p-6\">\n        {/* Banner Ad 1 */}\n        <div className=\"bg-white/5 rounded-lg p-4 mb-6 text-center\">\n          <p className=\"text-gray-400 text-xs mb-2\">Advertisement</p>\n          <div className=\"min-h-[120px] bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded flex items-center justify-center\">\n            <span className=\"text-gray-400\">Banner Ad Zone 1</span>\n          </div>\n        </div>\n\n        {/* Blog Content */}\n        <article className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n          <h1 className=\"text-3xl font-bold text-white mb-6\">\n            🎮 The Ultimate Guide to Gaming in 2024\n          </h1>\n\n          <div className=\"prose prose-invert max-w-none\">\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Gaming has evolved tremendously over the past decade, and 2024 promises to be one of the most exciting years yet for gamers worldwide.\n              From cutting-edge graphics to immersive virtual reality experiences, the gaming industry continues to push boundaries and redefine entertainment.\n            </p>\n\n            <h2 className=\"text-2xl font-semibold text-purple-300 mb-4 mt-8\">🚀 Latest Gaming Trends</h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              The rise of cloud gaming has made high-quality gaming accessible to more people than ever before. Services like Xbox Game Pass,\n              PlayStation Now, and Google Stadia have revolutionized how we access and play games. No longer do you need expensive hardware\n              to enjoy the latest AAA titles.\n            </p>\n\n            {/* Banner Ad 2 */}\n            <div className=\"bg-white/5 rounded-lg p-4 my-8 text-center\">\n              <p className=\"text-gray-400 text-xs mb-2\">Advertisement</p>\n              <div className=\"min-h-[120px] bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded flex items-center justify-center\">\n                <span className=\"text-gray-400\">Banner Ad Zone 2</span>\n              </div>\n            </div>\n\n            <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🎯 Best Gaming Platforms</h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Whether you're a PC master race enthusiast or a console loyalist, there's never been a better time to be a gamer.\n              The PlayStation 5 and Xbox Series X/S have brought 4K gaming and ray tracing to the mainstream, while the Nintendo Switch\n              continues to dominate the portable gaming market.\n            </p>\n\n            <ul className=\"text-gray-300 mb-6 space-y-2\">\n              <li>• <strong className=\"text-purple-300\">PC Gaming:</strong> Ultimate customization and performance</li>\n              <li>• <strong className=\"text-purple-300\">PlayStation 5:</strong> Exclusive titles and innovative DualSense controller</li>\n              <li>• <strong className=\"text-purple-300\">Xbox Series X/S:</strong> Game Pass integration and backward compatibility</li>\n              <li>• <strong className=\"text-purple-300\">Nintendo Switch:</strong> Portable gaming perfection</li>\n            </ul>\n\n            <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🏆 Top Games to Play Right Now</h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              The gaming landscape is filled with incredible titles across all genres. From epic single-player adventures to competitive\n              multiplayer experiences, there's something for every type of gamer.\n            </p>\n\n            {/* Banner Ad 3 */}\n            <div className=\"bg-white/5 rounded-lg p-4 my-8 text-center\">\n              <p className=\"text-gray-400 text-xs mb-2\">Advertisement</p>\n              <div className=\"min-h-[120px] bg-gradient-to-r from-green-900/30 to-blue-900/30 rounded flex items-center justify-center\">\n                <span className=\"text-gray-400\">Banner Ad Zone 3</span>\n              </div>\n            </div>\n\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Action-adventure games like \"The Legend of Zelda: Tears of the Kingdom\" and \"Spider-Man 2\" have set new standards\n              for open-world exploration and storytelling. Meanwhile, competitive games like \"Valorant\" and \"Apex Legends\"\n              continue to evolve with regular updates and seasonal content.\n            </p>\n\n            <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">💡 Gaming Tips for Beginners</h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Starting your gaming journey can be overwhelming with so many options available. Here are some essential tips\n              to help you get started and make the most of your gaming experience.\n            </p>\n\n            <div className=\"bg-purple-900/20 rounded-lg p-6 mb-6\">\n              <h3 className=\"text-xl font-semibold text-purple-300 mb-3\">Essential Gaming Setup</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• Invest in a comfortable gaming chair for long sessions</li>\n                <li>• Good headphones or speakers for immersive audio</li>\n                <li>• Stable internet connection for online gaming</li>\n                <li>• Proper lighting to reduce eye strain</li>\n              </ul>\n            </div>\n\n            {/* Banner Ad 4 */}\n            <div className=\"bg-white/5 rounded-lg p-4 my-8 text-center\">\n              <p className=\"text-gray-400 text-xs mb-2\">Advertisement</p>\n              <div className=\"min-h-[120px] bg-gradient-to-r from-pink-900/30 to-purple-900/30 rounded flex items-center justify-center\">\n                <span className=\"text-gray-400\">Banner Ad Zone 4</span>\n              </div>\n            </div>\n\n            <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🌟 The Future of Gaming</h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              As we look ahead, the future of gaming is incredibly bright. Virtual reality is becoming more accessible,\n              artificial intelligence is creating more dynamic game worlds, and cross-platform play is bringing gamers together\n              regardless of their chosen platform.\n            </p>\n\n            <p className=\"text-gray-300 mb-8 leading-relaxed\">\n              The integration of blockchain technology and NFTs in gaming is also creating new opportunities for players\n              to truly own their in-game assets. While still in its early stages, this technology could revolutionize\n              how we think about digital ownership in games.\n            </p>\n          </div>\n        </article>\n\n        {/* Continue Button - Only shows after 20 seconds */}\n        {showContinue && (\n          <div className=\"text-center mb-8\">\n            <button\n              onClick={handleContinue}\n              className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg\"\n            >\n              Continue to Next Step →\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Montag Banner Ad Script */}\n      <Script\n        dangerouslySetInnerHTML={{\n          __html: `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('gizokraijaw.net',9550341,document.createElement('script'))`\n        }}\n        strategy=\"afterInteractive\"\n      />\n\n      {/* PopAds Script */}\n      <Script\n        dangerouslySetInnerHTML={{\n          __html: `\n            (function(){var h=window,q=\"f698b60b6d08d4ddea0dff14beeaa446\",f=[[\"siteId\",905*337-585-19+4912789],[\"minBid\",0],[\"popundersPerIP\",\"0\"],[\"delayBetween\",0],[\"default\",false],[\"defaultPerDay\",0],[\"topmostLayer\",\"auto\"]],l=[\"d3d3LmJldHRlcmFkc3lzdGVtLmNvbS9CcGdEVVUvQS9qdmlzaWJseS5taW4uanM=\",\"ZDJrazBvM2ZyN2VkMDEuY2xvdWRmcm9udC5uZXQvbmV4aWYubWluLmpz\"],y=-1,d,x,m=function(){clearTimeout(x);y++;if(l[y]&&!(1778084907000<(new Date).getTime()&&1<y)){d=h.document.createElement(\"script\");d.type=\"text/javascript\";d.async=!0;var o=h.document.getElementsByTagName(\"script\")[0];d.src=\"https://\"+atob(l[y]);d.crossOrigin=\"anonymous\";d.onerror=m;d.onload=function(){clearTimeout(x);h[q.slice(0,16)+q.slice(0,16)]||m()};x=setTimeout(m,5E3);o.parentNode.insertBefore(d,o)}};if(!h[q]){try{Object.freeze(h[q]=f)}catch(e){}m()}})();\n          `\n        }}\n        strategy=\"afterInteractive\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,EAAE;oBACf,YAAY;gBACd,OAAO;oBACL,SAAS,KAAK,KAAK,IAAI;gBACzB;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,MAAM,QAAQ,WAAW;gBACvB,gBAAgB;YAClB,GAAG,QAAQ,aAAa;YAExB,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAM;IAEnB,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,MAAM,CAAC;IACrC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsF;;;;;;;;;;;;;;;;;;;;;;IAOzH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;gCAAmF;8CACvG,8OAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAElG,8OAAC;4BAAI,WAAU;sCAA4B;;;;;;;;;;;;;;;;;0BAM/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAKpC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDACjE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAOlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAIpC,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;oDAAG;kEAAE,8OAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAmB;;;;;;;0DAC7D,8OAAC;;oDAAG;kEAAE,8OAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAuB;;;;;;;0DACjE,8OAAC;;oDAAG;kEAAE,8OAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAyB;;;;;;;0DACnE,8OAAC;;oDAAG;kEAAE,8OAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAyB;;;;;;;;;;;;;kDAGrE,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAIpC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAKR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAIpC,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;oBASrD,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,8OAAC,8HAAA,CAAA,UAAM;gBACL,yBAAyB;oBACvB,QAAQ,CAAC,mLAAmL,CAAC;gBAC/L;gBACA,UAAS;;;;;;0BAIX,8OAAC,8HAAA,CAAA,UAAM;gBACL,yBAAyB;oBACvB,QAAQ,CAAC;;UAET,CAAC;gBACH;gBACA,UAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}