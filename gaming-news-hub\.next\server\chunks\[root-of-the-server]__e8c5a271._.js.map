{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/urlStore.ts"], "sourcesContent": ["// Hybrid URL store - uses database in production, memory locally\nexport interface UrlData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: Date;\n}\n\n// In-memory store for local development\nclass MemoryUrlStore {\n  private store = new Map<string, UrlData>();\n\n  async set(shortCode: string, data: UrlData): Promise<void> {\n    this.store.set(shortCode, data);\n  }\n\n  async get(shortCode: string): Promise<UrlData | undefined> {\n    return this.store.get(shortCode);\n  }\n\n  async has(shortCode: string): Promise<boolean> {\n    return this.store.has(shortCode);\n  }\n\n  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {\n    return Array.from(this.store.entries()).map(([shortCode, data]) => ({\n      shortCode,\n      ...data\n    }));\n  }\n\n  async incrementClicks(shortCode: string): Promise<boolean> {\n    const data = this.store.get(shortCode);\n    if (data) {\n      data.clicks += 1;\n      this.store.set(shortCode, data);\n      return true;\n    }\n    return false;\n  }\n}\n\n// Database store for production\nclass DatabaseUrlStore {\n  private initialized = false;\n\n  async init() {\n    if (!this.initialized) {\n      const { initDatabase } = await import('./database');\n      await initDatabase();\n      this.initialized = true;\n    }\n  }\n\n  async set(shortCode: string, data: UrlData): Promise<void> {\n    await this.init();\n    const { createShortUrl } = await import('./database');\n    await createShortUrl(shortCode, data.originalUrl);\n  }\n\n  async get(shortCode: string): Promise<UrlData | undefined> {\n    await this.init();\n    const { getUrlByShortCode } = await import('./database');\n    const record = await getUrlByShortCode(shortCode);\n    if (!record) return undefined;\n\n    return {\n      originalUrl: record.original_url,\n      clicks: record.clicks,\n      createdAt: record.created_at\n    };\n  }\n\n  async has(shortCode: string): Promise<boolean> {\n    await this.init();\n    const { checkShortCodeExists } = await import('./database');\n    return await checkShortCodeExists(shortCode);\n  }\n\n  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {\n    await this.init();\n    const { getRecentLinks } = await import('./database');\n    const records = await getRecentLinks();\n\n    return records.map(record => ({\n      shortCode: record.short_code,\n      originalUrl: record.original_url,\n      clicks: record.clicks,\n      createdAt: record.created_at\n    }));\n  }\n\n  async incrementClicks(shortCode: string): Promise<boolean> {\n    await this.init();\n    try {\n      const { incrementClicks } = await import('./database');\n      await incrementClicks(shortCode);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\n// Choose store based on environment\nfunction createUrlStore() {\n  // Use database if DATABASE_URL is provided, otherwise use memory\n  if (process.env.DATABASE_URL) {\n    console.log('Using database store');\n    return new DatabaseUrlStore();\n  } else {\n    console.log('Using memory store (local development)');\n    return new MemoryUrlStore();\n  }\n}\n\n// Export a singleton instance\nexport const urlStore = createUrlStore();\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;AAOjE,wCAAwC;AACxC,MAAM;IACI,QAAQ,IAAI,MAAuB;IAE3C,MAAM,IAAI,SAAiB,EAAE,IAAa,EAAiB;QACzD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW;IAC5B;IAEA,MAAM,IAAI,SAAiB,EAAgC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,MAAM,IAAI,SAAiB,EAAoB;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,MAAM,SAA0D;QAC9D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,KAAK,GAAK,CAAC;gBAClE;gBACA,GAAG,IAAI;YACT,CAAC;IACH;IAEA,MAAM,gBAAgB,SAAiB,EAAoB;QACzD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,MAAM;YACR,KAAK,MAAM,IAAI;YACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW;YAC1B,OAAO;QACT;QACA,OAAO;IACT;AACF;AAEA,gCAAgC;AAChC,MAAM;IACI,cAAc,MAAM;IAE5B,MAAM,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,MAAM;YACN,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,MAAM,IAAI,SAAiB,EAAE,IAAa,EAAiB;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,eAAe,WAAW,KAAK,WAAW;IAClD;IAEA,MAAM,IAAI,SAAiB,EAAgC;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAC9B,MAAM,SAAS,MAAM,kBAAkB;QACvC,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAO;YACL,aAAa,OAAO,YAAY;YAChC,QAAQ,OAAO,MAAM;YACrB,WAAW,OAAO,UAAU;QAC9B;IACF;IAEA,MAAM,IAAI,SAAiB,EAAoB;QAC7C,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,oBAAoB,EAAE,GAAG;QACjC,OAAO,MAAM,qBAAqB;IACpC;IAEA,MAAM,SAA0D;QAC9D,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,UAAU,MAAM;QAEtB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,aAAa,OAAO,YAAY;gBAChC,QAAQ,OAAO,MAAM;gBACrB,WAAW,OAAO,UAAU;YAC9B,CAAC;IACH;IAEA,MAAM,gBAAgB,SAAiB,EAAoB;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,gBAAgB;YACtB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,oCAAoC;AACpC,SAAS;IACP,iEAAiE;IACjE,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI;IACb,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI;IACb;AACF;AAGO,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/api/shorten/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { urlStore } from '@/lib/urlStore';\n\n// Generate a random short code\nfunction generateShortCode(): string {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = '';\n  for (let i = 0; i < 6; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// Validate URL format\nfunction isValidUrl(string: string): boolean {\n  try {\n    new URL(string);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json({ error: 'URL is required' }, { status: 400 });\n    }\n\n    if (!isValidUrl(url)) {\n      return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });\n    }\n\n    // Generate unique short code\n    let shortCode = generateShortCode();\n    while (await urlStore.has(shortCode)) {\n      shortCode = generateShortCode();\n    }\n\n    // Store in database\n    await urlStore.set(shortCode, {\n      originalUrl: url,\n      clicks: 0,\n      createdAt: new Date()\n    });\n\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;\n    const shortUrl = `${baseUrl}/s/${shortCode}`;\n\n    return NextResponse.json({\n      shortUrl,\n      shortCode,\n      originalUrl: url\n    });\n\n  } catch (error) {\n    console.error('Error shortening URL:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n\nexport async function GET() {\n  // Return recent links for the dashboard\n  const recentLinks = (await urlStore.getAll())\n    .map((data) => ({\n      ...data,\n      shortUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/s/${data.shortCode}`\n    }))\n    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n    .slice(0, 10);\n\n  return NextResponse.json({ links: recentLinks });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,+BAA+B;AAC/B,SAAS;IACP,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAEA,sBAAsB;AACtB,SAAS,WAAW,MAAc;IAChC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,6BAA6B;QAC7B,IAAI,YAAY;QAChB,MAAO,MAAM,wHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,WAAY;YACpC,YAAY;QACd;QAEA,oBAAoB;QACpB,MAAM,wHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,WAAW;YAC5B,aAAa;YACb,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,MAAM,UAAU,6DAAoC,QAAQ,OAAO,CAAC,MAAM;QAC1E,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,WAAW;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA,aAAa;QACf;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe;IACpB,wCAAwC;IACxC,MAAM,cAAc,CAAC,MAAM,wHAAA,CAAA,WAAQ,CAAC,MAAM,EAAE,EACzC,GAAG,CAAC,CAAC,OAAS,CAAC;YACd,GAAG,IAAI;YACP,UAAU,GAAG,6DAAoC,wBAAwB,GAAG,EAAE,KAAK,SAAS,EAAE;QAChG,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,IAC1D,KAAK,CAAC,GAAG;IAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,OAAO;IAAY;AAChD", "debugId": null}}]}